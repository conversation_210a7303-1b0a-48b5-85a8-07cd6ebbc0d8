# GraspLDM Unified Demo Documentation

## Overview

The GraspLDM inference capabilities have been consolidated into a single, comprehensive demonstration file: `simple_inference_example.py`. This unified approach replaces multiple scattered test files with a well-organized, feature-complete demonstration suite.

## What Was Done

### File Consolidation
- **Merged files**: `test_enhanced_inference.py`, `test_enhanced_visualization.py`, `test_simple_inference.py`
- **Target file**: `examples/simple_inference_example.py`
- **Deleted files**: All original test files have been removed
- **Fixed**: Errors in the original `demo_simple_inference()` function in `tools/simple_inference.py`

### Features Included
The unified demo includes demonstrations of all features actually implemented in `tools/simple_inference.py`:

1. **Basic Functionality**
   - Point cloud preprocessing and inference
   - Synthetic data generation (sphere, cube, cylinder)
   - Camera pose transformations

2. **Advanced Features**
   - 6DOF-GraspNet evaluation integration
   - Grasp refinement with multiple strategies
   - Enhanced visualization capabilities

3. **Specialized Tools**
   - Diffusion animation creation
   - Multiple output format support
   - File I/O operations

4. **Testing & Validation**
   - Visualization method testing
   - Integration testing

### Command Line Interface

The unified demo provides a comprehensive CLI with options for:
- Selective demo execution (`--demo_basic`, `--demo_6dof`, etc.)
- 6DOF integration configuration (`--enable_6dof`, `--enable_refinement`)
- Advanced parameter tuning (`--refinement_method`, `--refinement_steps`)
- File input/output (`--pc_file`)

## Usage Examples

```bash
# Basic demo with synthetic data
python examples/simple_inference_example.py --exp_path checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k

# Full demo with all features
python examples/simple_inference_example.py --exp_path <exp_path> --demo_all --enable_6dof --enable_refinement

# Demo with real point cloud file
python examples/simple_inference_example.py --exp_path <exp_path> --pc_file data/example.ply --enable_6dof

# Test visualization only
python examples/simple_inference_example.py --exp_path <exp_path> --test_visualization_only
```

## Demo Functions

| Function | Description | Dependencies |
|----------|-------------|--------------|
| `demo_basic_inference` | Basic grasp generation | Core GraspLDM |
| `demo_camera_pose_transformation` | Camera coordinate handling | Core GraspLDM |
| `demo_6dof_evaluation` | Quality assessment | 6DOF-GraspNet |
| `demo_6dof_refinement` | Grasp optimization | 6DOF-GraspNet |
| `demo_enhanced_visualization` | Advanced visualization | Optional 6DOF |
| `demo_diffusion_animation` | Process animation | Core GraspLDM |
| `demo_file_input_output` | File operations | Core GraspLDM |
| `demo_comprehensive_pipeline` | Full feature demo | All available |

## Key Improvements

1. **Code Organization**: Clear separation of utilities, demos, and interfaces
2. **Error Handling**: Robust error handling with informative messages
3. **Flexibility**: Configurable demo execution based on available features
4. **Documentation**: Comprehensive docstrings and usage examples
5. **Maintenance**: Single file to maintain instead of multiple scattered files

## Validation

The unified demo includes validation that:
- Only uses features actually implemented in `simple_inference.py`
- Gracefully handles missing dependencies (e.g., 6DOF-GraspNet)
- Provides clear feedback on feature availability
- Maintains backward compatibility with existing workflows

## Migration Note

If you were previously using separate test files, they have been removed. All functionality is now available through `simple_inference_example.py` with improved organization and additional features. 