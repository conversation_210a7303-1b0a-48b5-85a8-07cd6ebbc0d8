#!/usr/bin/env python3
"""
Unified GraspLDM Enhanced Inference Demo

This comprehensive demo script showcases all capabilities of the SimpleGraspLDMInference
class, integrating functionality from multiple test files into a single organized demonstration.

Features demonstrated:
- Basic grasp generation with synthetic data
- 6DOF-GraspNet evaluation and refinement
- Enhanced visualization capabilities  
- Point cloud file loading and processing
- Camera pose transformations
- Diffusion animation creation
- Various output formats and saving options

Usage:
    # Basic demo with synthetic data
    python examples/simple_inference_example.py --exp_path <experiment_path>
    
    # Demo with point cloud file
    python examples/simple_inference_example.py --exp_path <experiment_path> --pc_file <file.ply>
    
    # Enable all advanced features
    python examples/simple_inference_example.py --exp_path <experiment_path> --enable_6dof --enable_refinement --visualize
    
    # Test enhanced visualization only
    python examples/simple_inference_example.py --exp_path <experiment_path> --test_visualization_only

Examples:
    python examples/simple_inference_example.py --exp_path checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k
    python examples/simple_inference_example.py --exp_path <exp_path> --pc_file data/example.ply --enable_6dof
"""

import argparse
import os
import sys
import warnings
import numpy as np
import torch
from typing import Optional, Tuple, Dict, Any

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from tools.simple_inference import SimpleGraspLDMInference, SIXDOF_AVAILABLE
    print(f"✓ Successfully imported SimpleGraspLDMInference")
    print(f"  6DOF-GraspNet available: {SIXDOF_AVAILABLE}")
except ImportError as e:
    print(f"❌ Failed to import SimpleGraspLDMInference: {e}")
    print("Please ensure you're running from the correct directory and all dependencies are installed")
    sys.exit(1)


# =============================================================================
# SYNTHETIC DATA GENERATION UTILITIES
# =============================================================================

def create_synthetic_pointcloud(shape: str = "sphere", num_points: int = 2000, scale: float = 0.1) -> np.ndarray:
    """
    Create synthetic point clouds for testing and demonstration.
    
    Args:
        shape: Shape to generate ('sphere', 'cube', 'cylinder')
        num_points: Number of points to generate
        scale: Scale factor for the object size
        
    Returns:
        np.ndarray: Point cloud array [num_points, 3]
    """
    np.random.seed(42)  # For reproducible demos
    
    if shape == "sphere":
        # Generate sphere point cloud using spherical coordinates
        theta = np.random.uniform(0, 2*np.pi, num_points)
        phi = np.random.uniform(0, np.pi, num_points)
        r = scale
        
        x = r * np.sin(phi) * np.cos(theta)
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)
        
        return np.column_stack([x, y, z])
    
    elif shape == "cube":
        # Generate cube surface points
        points = []
        points_per_face = num_points // 6
        
        for face in range(6):
            if face == 0:  # +X face
                x = np.full(points_per_face, scale)
                y = np.random.uniform(-scale, scale, points_per_face)
                z = np.random.uniform(-scale, scale, points_per_face)
            elif face == 1:  # -X face
                x = np.full(points_per_face, -scale)
                y = np.random.uniform(-scale, scale, points_per_face)
                z = np.random.uniform(-scale, scale, points_per_face)
            elif face == 2:  # +Y face
                x = np.random.uniform(-scale, scale, points_per_face)
                y = np.full(points_per_face, scale)
                z = np.random.uniform(-scale, scale, points_per_face)
            elif face == 3:  # -Y face
                x = np.random.uniform(-scale, scale, points_per_face)
                y = np.full(points_per_face, -scale)
                z = np.random.uniform(-scale, scale, points_per_face)
            elif face == 4:  # +Z face
                x = np.random.uniform(-scale, scale, points_per_face)
                y = np.random.uniform(-scale, scale, points_per_face)
                z = np.full(points_per_face, scale)
            else:  # -Z face
                x = np.random.uniform(-scale, scale, points_per_face)
                y = np.random.uniform(-scale, scale, points_per_face)
                z = np.full(points_per_face, -scale)
            
            points.append(np.column_stack([x, y, z]))
        
        return np.vstack(points)
    
    elif shape == "cylinder":
        # Generate cylinder point cloud
        # Side surface
        side_points = int(num_points * 0.8)
        theta = np.random.uniform(0, 2*np.pi, side_points)
        z = np.random.uniform(-scale, scale, side_points)
        x = scale * 0.8 * np.cos(theta)
        y = scale * 0.8 * np.sin(theta)
        side_surface = np.column_stack([x, y, z])
        
        # Top and bottom circles
        cap_points = num_points - side_points
        cap_points_each = cap_points // 2
        
        # Top circle
        theta_top = np.random.uniform(0, 2*np.pi, cap_points_each)
        r_top = np.random.uniform(0, scale * 0.8, cap_points_each)
        x_top = r_top * np.cos(theta_top)
        y_top = r_top * np.sin(theta_top)
        z_top = np.full(cap_points_each, scale)
        top_circle = np.column_stack([x_top, y_top, z_top])
        
        # Bottom circle  
        theta_bottom = np.random.uniform(0, 2*np.pi, cap_points_each)
        r_bottom = np.random.uniform(0, scale * 0.8, cap_points_each)
        x_bottom = r_bottom * np.cos(theta_bottom)
        y_bottom = r_bottom * np.sin(theta_bottom)
        z_bottom = np.full(cap_points_each, -scale)
        bottom_circle = np.column_stack([x_bottom, y_bottom, z_bottom])
        
        return np.vstack([side_surface, top_circle, bottom_circle])
    
    else:
        raise ValueError(f"Unknown shape: {shape}. Supported shapes: 'sphere', 'cube', 'cylinder'")


def create_camera_pose(translation: list = [0, 0, 0.3], rotation_deg: list = [0, 0, 0]) -> np.ndarray:
    """
    Create a camera pose transformation matrix.
    
    Args:
        translation: Camera translation [x, y, z] in meters
        rotation_deg: Camera rotation [rx, ry, rz] in degrees
        
    Returns:
        np.ndarray: 4x4 homogeneous transformation matrix
    """
    # Convert rotation from degrees to radians
    rx, ry, rz = np.radians(rotation_deg)
    
    # Create rotation matrices
    Rx = np.array([[1, 0, 0],
                   [0, np.cos(rx), -np.sin(rx)],
                   [0, np.sin(rx), np.cos(rx)]])
    
    Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                   [0, 1, 0],
                   [-np.sin(ry), 0, np.cos(ry)]])
    
    Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                   [np.sin(rz), np.cos(rz), 0],
                   [0, 0, 1]])
    
    # Combined rotation (ZYX order)
    R = Rz @ Ry @ Rx
    
    # Create 4x4 transformation matrix
    T = np.eye(4)
    T[:3, :3] = R
    T[:3, 3] = translation
    
    return T


# =============================================================================
# DEMONSTRATION FUNCTIONS
# =============================================================================

def demo_basic_inference(inference_engine: SimpleGraspLDMInference) -> Dict[str, Any]:
    """
    Demonstrate basic grasp generation with synthetic point clouds.
    
    This demo shows the core functionality without advanced features.
    """
    print("\n" + "="*70)
    print("🔹 DEMO 1: Basic Grasp Generation")
    print("="*70)
    
    # Create synthetic sphere
    pc = create_synthetic_pointcloud("sphere", num_points=1500, scale=0.08)
    print(f"Created sphere point cloud: {pc.shape}")
    
    # Generate grasps
    print("\n🤖 Generating grasps...")
    results = inference_engine.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=15,
        visualize=False
    )
    
    # Analyze results
    confidence = results["confidence"].squeeze()
    print(f"\n📊 Results Analysis:")
    print(f"  Generated grasps: {len(confidence)}")
    print(f"  Confidence range: {confidence.min():.3f} - {confidence.max():.3f}")
    print(f"  Mean confidence: {confidence.mean():.3f}")
    print(f"  Point cloud processed: {results['pc'].shape}")
    
    return results


def demo_camera_pose_transformation(inference_engine: SimpleGraspLDMInference) -> Dict[str, Any]:
    """
    Demonstrate inference with camera pose transformations.
    
    Shows how to handle point clouds in camera coordinates.
    """
    print("\n" + "="*70)
    print("🔹 DEMO 2: Camera Pose Transformation")
    print("="*70)
    
    # Create synthetic cube in camera coordinates
    pc_camera = create_synthetic_pointcloud("cube", num_points=1200, scale=0.06)
    print(f"Created cube point cloud in camera coordinates: {pc_camera.shape}")
    
    # Create camera pose (camera looking down at object)
    camera_pose = create_camera_pose(
        translation=[0, 0, 0.3],  # 30cm above object
        rotation_deg=[180, 0, 0]  # Looking down
    )
    print(f"Camera pose transformation matrix:")
    print(f"  Translation: {camera_pose[:3, 3]}")
    print(f"  Rotation angles: [180°, 0°, 0°] (looking down)")
    
    # Generate grasps with camera transformation
    print("\n🤖 Generating grasps with camera transformation...")
    results = inference_engine.infer_from_pointcloud(
        pointcloud=pc_camera,
        camera_pose=camera_pose,
        num_grasps=20,
        visualize=False
    )
    
    # Analyze transformation effects
    original_mean = np.mean(pc_camera, axis=0)
    transformed_mean = torch.mean(results["pc"], dim=0).cpu().numpy()
    
    print(f"\n📊 Transformation Analysis:")
    print(f"  Original PC centroid: [{original_mean[0]:.3f}, {original_mean[1]:.3f}, {original_mean[2]:.3f}]")
    print(f"  Transformed PC centroid: [{transformed_mean[0]:.3f}, {transformed_mean[1]:.3f}, {transformed_mean[2]:.3f}]")
    print(f"  Generated grasps: {results['grasps'].shape[0]}")
    print(f"  Mean confidence: {results['confidence'].mean():.3f}")
    
    return results


def demo_6dof_evaluation(inference_engine: SimpleGraspLDMInference) -> Optional[Dict[str, Any]]:
    """
    Demonstrate 6DOF-GraspNet evaluation capabilities.
    
    Shows how grasp quality assessment works with the 6DOF evaluator.
    """
    print("\n" + "="*70)
    print("🔹 DEMO 3: 6DOF-GraspNet Evaluation")
    print("="*70)
    
    if not SIXDOF_AVAILABLE:
        print("⚠️  6DOF-GraspNet not available, skipping evaluation demo")
        return None
        
    if not inference_engine.enable_grasp_evaluation:
        print("⚠️  6DOF evaluation not enabled in inference engine")
        return None
    
    # Create synthetic cylinder for evaluation
    pc = create_synthetic_pointcloud("cylinder", num_points=1800, scale=0.10)
    print(f"Created cylinder point cloud: {pc.shape}")
    
    # Generate grasps with 6DOF evaluation
    print("\n🤖 Generating grasps with 6DOF evaluation...")
    results = inference_engine.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=25,
        evaluate_with_6dof=True,
        visualize=False
    )
    
    # Compare scoring methods
    graspldm_conf = results['confidence'].squeeze()
    sixdof_scores = results['sixdof_scores']
    
    print(f"\n📊 Evaluation Comparison:")
    print(f"  Generated grasps: {len(graspldm_conf)}")
    print(f"  GraspLDM confidence: {graspldm_conf.mean():.3f} ± {graspldm_conf.std():.3f}")
    print(f"  6DOF-GraspNet scores: {sixdof_scores.mean():.3f} ± {sixdof_scores.std():.3f}")
    
    # Demonstrate filtering by 6DOF scores
    print(f"\n🔍 Demonstrating 6DOF score filtering...")
    high_quality_grasps = inference_engine.filter_grasps_by_6dof_score(results, min_score=0.5)
    top_grasps = inference_engine.get_best_grasps_by_6dof(results, top_k=5)
    
    print(f"  High-quality grasps (≥0.5): {high_quality_grasps['grasps'].shape[0]}")
    print(f"  Top-5 6DOF scores: {top_grasps['sixdof_scores'].tolist()}")
    
    return results


def demo_6dof_refinement(inference_engine: SimpleGraspLDMInference) -> Optional[Dict[str, Any]]:
    """
    Demonstrate grasp refinement using 6DOF-GraspNet optimization.
    
    Shows how grasps can be improved through iterative refinement.
    """
    print("\n" + "="*70)
    print("🔹 DEMO 4: Grasp Refinement")
    print("="*70)
    
    if not SIXDOF_AVAILABLE:
        print("⚠️  6DOF-GraspNet not available, skipping refinement demo")
        return None
        
    if not inference_engine.enable_grasp_refinement:
        print("⚠️  Grasp refinement not enabled in inference engine")
        return None
    
    # Create synthetic object for refinement
    pc = create_synthetic_pointcloud("sphere", num_points=1600, scale=0.12)
    print(f"Created sphere point cloud: {pc.shape}")
    
    # Generate and refine grasps
    print(f"\n🔧 Generating grasps with refinement...")
    print(f"  Refinement method: {inference_engine.refinement_method}")
    print(f"  Refinement steps: {inference_engine.refinement_steps}")
    
    results = inference_engine.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=15,
        refine_grasps=True,
        visualize=False
    )
    
    # Analyze refinement improvements
    if 'sixdof_scores' in results and 'refined_scores' in results:
        initial_scores = results['sixdof_scores'] 
        refined_scores = results['refined_scores']
        
        improvement = refined_scores.mean() - initial_scores.mean()
        improved_count = (refined_scores > initial_scores).sum().item()
        
        print(f"\n📊 Refinement Analysis:")
        print(f"  Initial mean score: {initial_scores.mean():.3f}")
        print(f"  Refined mean score: {refined_scores.mean():.3f}")
        print(f"  Average improvement: {improvement:+.3f}")
        print(f"  Improved grasps: {improved_count}/{len(refined_scores)}")
        print(f"  Final grasp count: {results['grasps'].shape[0]}")
    else:
        print("⚠️  Refinement scores not available in results")
    
    return results


def demo_enhanced_visualization(inference_engine: SimpleGraspLDMInference, 
                              results: Dict[str, Any]) -> bool:
    """
    Demonstrate enhanced visualization capabilities.
    
    Shows different visualization options and export formats.
    """
    print("\n" + "="*70)
    print("🔹 DEMO 5: Enhanced Visualization")
    print("="*70)
    
    if results is None:
        print("⚠️  No results provided, generating new data for visualization...")
        pc = create_synthetic_pointcloud("sphere", num_points=1000, scale=0.09)
        results = inference_engine.infer_from_pointcloud(
            pointcloud=pc,
            num_grasps=12,
            visualize=False
        )
    
    success_count = 0
    
    # Test standard visualization save
    print("\n💾 Testing standard visualization save...")
    try:
        success = inference_engine.save_visualization(
            results, 
            save_path="demo_standard_visualization.glb",
            include_confidence_colors=True
        )
        if success:
            print("  ✅ Standard visualization saved successfully")
            success_count += 1
        else:
            print("  ❌ Standard visualization save failed")
    except Exception as e:
        print(f"  ❌ Standard visualization error: {e}")
    
    # Test enhanced visualization if available
    if SIXDOF_AVAILABLE:
        print("\n🎨 Testing enhanced 6DOF visualization...")
        try:
            scene = inference_engine.visualize_enhanced(
                results=results,
                save_path="demo_enhanced_visualization.glb",
                show_scene=False,
                return_scene=True,
                visualize_diverse_grasps=True,
                min_separation_distance=0.03,
                plasma_coloring=True,
                show_gripper_mesh=False,
                fallback_to_standard=True
            )
            if scene is not None:
                print("  ✅ Enhanced visualization created successfully")
                success_count += 1
            else:
                print("  ⚠️  Enhanced visualization returned None")
        except Exception as e:
            print(f"  ❌ Enhanced visualization error: {e}")
    else:
        print("\n⚠️  Enhanced visualization not available (6DOF-GraspNet required)")
    
    return success_count > 0


def demo_diffusion_animation(inference_engine: SimpleGraspLDMInference) -> bool:
    """
    Demonstrate diffusion animation creation.
    
    Shows the grasp evolution during the denoising process.
    """
    print("\n" + "="*70)
    print("🔹 DEMO 6: Diffusion Process Animation")
    print("="*70)
    
    # Generate grasps with intermediate steps
    pc = create_synthetic_pointcloud("cube", num_points=1000, scale=0.07)
    print(f"Created cube point cloud: {pc.shape}")
    
    print("\n🎬 Generating grasps with intermediate steps...")
    results = inference_engine.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=8,
        return_intermediate=True,
        visualize=False
    )
    
    if not results.get('all_steps_grasps'):
        print("❌ No intermediate steps captured")
        return False
    
    print(f"  Captured {len(results['all_steps_grasps'])} diffusion steps")
    
    # Create animation
    print("\n🎞️  Creating diffusion animation...")
    try:
        success = inference_engine.create_diffusion_animation(
            results=results,
            save_path="demo_diffusion_evolution.gif",
            resolution=(800, 600),
            fps=2,
            show_confidence_colors=True,
            show_progress=True
        )
        
        if success:
            print("  ✅ Diffusion animation created successfully")
            return True
        else:
            print("  ❌ Animation creation failed")
            return False
            
    except Exception as e:
        print(f"  ❌ Animation error: {e}")
        return False


def demo_file_input_output(inference_engine: SimpleGraspLDMInference, 
                          pc_file: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Demonstrate point cloud file loading and various output formats.
    
    Shows how to work with real point cloud data files.
    """
    print("\n" + "="*70)
    print("🔹 DEMO 7: File Input/Output")
    print("="*70)
    
    if pc_file and os.path.exists(pc_file):
        print(f"📁 Loading point cloud from file: {pc_file}")
        try:
            pc = inference_engine.load_pointcloud_from_file(pc_file)
            print(f"  Loaded point cloud: {pc.shape}")
            
            # Generate grasps from file
            results = inference_engine.infer_from_pointcloud(
                pointcloud=pc,
                num_grasps=20,
                visualize=False
            )
            
            print(f"  Generated {results['grasps'].shape[0]} grasps from file")
            
            # Test multiple output formats
            formats = [".glb", ".ply", ".obj"]
            for fmt in formats:
                try:
                    save_path = f"demo_file_output{fmt}"
                    success = inference_engine.save_visualization(results, save_path)
                    if success:
                        print(f"  ✅ Saved {fmt} format successfully")
                    else:
                        print(f"  ❌ Failed to save {fmt} format")
                except Exception as e:
                    print(f"  ❌ Error saving {fmt}: {e}")
            
            return results
            
        except Exception as e:
            print(f"❌ Failed to load point cloud file: {e}")
            return None
    else:
        print("⚠️  No valid point cloud file provided, skipping file I/O demo")
        if pc_file:
            print(f"   File not found: {pc_file}")
        return None


def demo_comprehensive_pipeline(inference_engine: SimpleGraspLDMInference) -> bool:
    """
    Demonstrate the complete pipeline with all available features.
    
    This is a comprehensive demo that combines multiple capabilities.
    """
    print("\n" + "="*70)
    print("🔹 DEMO 8: Comprehensive Pipeline")
    print("="*70)
    
    # Create complex synthetic object
    pc = create_synthetic_pointcloud("cylinder", num_points=2000, scale=0.15)
    print(f"Created cylinder point cloud: {pc.shape}")
    
    # Determine available features
    enable_6dof = inference_engine.enable_grasp_evaluation
    enable_refinement = inference_engine.enable_grasp_refinement
    
    print(f"\n🔧 Pipeline Configuration:")
    print(f"  6DOF Evaluation: {'✓' if enable_6dof else '✗'}")
    print(f"  Grasp Refinement: {'✓' if enable_refinement else '✗'}")
    print(f"  Enhanced Visualization: {'✓' if SIXDOF_AVAILABLE else '✗'}")
    
    # Run comprehensive inference
    print(f"\n🚀 Running comprehensive inference...")
    try:
        results = inference_engine.infer_from_pointcloud(
            pointcloud=pc,
            num_grasps=30,
            return_intermediate=True,
            evaluate_with_6dof=enable_6dof,
            refine_grasps=enable_refinement,
            visualize=False
        )
        
        # Comprehensive analysis
        print(f"\n📊 Comprehensive Results:")
        print(f"  Generated grasps: {results['grasps'].shape[0]}")
        print(f"  GraspLDM confidence: {results['confidence'].mean():.3f}")
        
        if 'sixdof_scores' in results:
            print(f"  6DOF scores: {results['sixdof_scores'].mean():.3f}")
            
        if 'refined_scores' in results:
            print(f"  Refined scores: {results['refined_scores'].mean():.3f}")
            
        if 'all_steps_grasps' in results:
            print(f"  Diffusion steps: {len(results['all_steps_grasps'])}")
        
        # Save comprehensive results
        save_success = inference_engine.save_visualization(
            results, 
            "demo_comprehensive_pipeline.glb"
        )
        
        if save_success:
            print(f"  ✅ Comprehensive results saved")
        
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive pipeline failed: {e}")
        import traceback
        traceback.print_exc()
        return False


# =============================================================================
# VISUALIZATION-ONLY TESTING FUNCTIONS
# =============================================================================

def test_visualization_methods(inference_engine: SimpleGraspLDMInference) -> bool:
    """
    Test visualization functionality in isolation.
    
    This function specifically tests the visualization refactoring and methods.
    """
    print("\n" + "="*70)
    print("🔹 VISUALIZATION TESTING: Method Validation")
    print("="*70)
    
    # Create mock results for testing
    print("📊 Creating mock inference results...")
    pc = create_synthetic_pointcloud("sphere", num_points=1024, scale=0.12)
    
    # Generate simple results for testing
    results = inference_engine.infer_from_pointcloud(
        pointcloud=pc,
        num_grasps=10,
        visualize=False
    )
    
    print(f"✓ Mock data created:")
    print(f"  Point cloud: {results['pc'].shape}")
    print(f"  Grasps: {results['grasps'].shape}")
    print(f"  Confidence: {results['confidence'].shape}")
    
    test_count = 0
    passed_count = 0
    
    # Test 1: Standard visualization method
    print(f"\n🧪 Test 1: Standard visualization method...")
    try:
        scene = inference_engine._visualize_results(
            results, return_scene=True, save_path="test_standard.glb"
        )
        print(f"  ✅ Standard visualization method works")
        passed_count += 1
    except Exception as e:
        print(f"  ❌ Standard visualization failed: {e}")
    test_count += 1
    
    # Test 2: Enhanced visualization method (if available)
    if hasattr(inference_engine, '_visualize_with_6dof_scene') and SIXDOF_AVAILABLE:
        print(f"\n🧪 Test 2: Enhanced 6DOF visualization method...")
        try:
            scene = inference_engine._visualize_with_6dof_scene(
                results=results,
                save_path="test_enhanced.glb",
                show_scene=False,
                return_scene=True
            )
            print(f"  ✅ Enhanced visualization method works")
            passed_count += 1
        except Exception as e:
            print(f"  ❌ Enhanced visualization failed: {e}")
        test_count += 1
    else:
        print(f"\n⚠️  Test 2: Enhanced visualization not available")
    
    # Test 3: Public enhanced visualization interface
    if hasattr(inference_engine, 'visualize_enhanced'):
        print(f"\n🧪 Test 3: Public enhanced visualization interface...")
        try:
            scene = inference_engine.visualize_enhanced(
                results=results,
                save_path="test_public_enhanced.glb",
                show_scene=False,
                return_scene=True,
                fallback_to_standard=True
            )
            print(f"  ✅ Public enhanced visualization interface works")
            passed_count += 1
        except Exception as e:
            print(f"  ❌ Public enhanced visualization failed: {e}")
        test_count += 1
    else:
        print(f"\n❌ Test 3: visualize_enhanced method not found")
        test_count += 1
    
    # Test 4: Save visualization utility
    print(f"\n🧪 Test 4: Save visualization utility...")
    try:
        success = inference_engine.save_visualization(
            results, "test_save_utility.glb"
        )
        if success:
            print(f"  ✅ Save visualization utility works")
            passed_count += 1
        else:
            print(f"  ❌ Save visualization utility returned False")
    except Exception as e:
        print(f"  ❌ Save visualization utility failed: {e}")
    test_count += 1
    
    # Summary
    print(f"\n📊 Visualization Testing Summary:")
    print(f"  Tests passed: {passed_count}/{test_count}")
    print(f"  Success rate: {passed_count/test_count*100:.1f}%")
    
    return passed_count == test_count


# =============================================================================
# MAIN DEMONSTRATION CONTROLLER
# =============================================================================

def run_unified_demo(args) -> bool:
    """
    Run the unified demonstration based on command line arguments.
    
    This is the main controller that orchestrates all demonstration functions.
    """
    print("🚀 GraspLDM Enhanced Inference - Unified Demo")
    print("="*80)
    print(f"Experiment path: {args.exp_path}")
    print(f"Device: {args.device}")
    print(f"6DOF-GraspNet available: {SIXDOF_AVAILABLE}")
    
    # Initialize inference engine with appropriate settings
    try:
        print(f"\n⚙️  Initializing inference engine...")
        
        inference = SimpleGraspLDMInference(
            exp_path=args.exp_path,
            device=args.device,
            enable_grasp_evaluation=args.enable_6dof and SIXDOF_AVAILABLE,
            enable_grasp_refinement=args.enable_refinement and SIXDOF_AVAILABLE,
            evaluator_model_path=args.evaluator_model_path,
            refinement_method=args.refinement_method,
            refinement_steps=args.refinement_steps,
            refinement_threshold=args.refinement_threshold,
            choose_fn=args.choose_fn,
            num_inference_steps=args.fast_steps,
            use_fast_sampler=True
        )
        
        print(f"✅ Inference engine initialized successfully")
        
    except Exception as e:
        print(f"❌ Failed to initialize inference engine: {e}")
        return False
    
    # Special case: visualization testing only
    if args.test_visualization_only:
        return test_visualization_methods(inference)
    
    # Run selected demonstrations
    demo_results = {}
    success_count = 0
    
    if args.demo_basic or args.demo_all:
        try:
            demo_results['basic'] = demo_basic_inference(inference)
            success_count += 1
            print("✅ Basic inference demo completed")
        except Exception as e:
            print(f"❌ Basic inference demo failed: {e}")
    
    if args.demo_camera or args.demo_all:
        try:
            demo_results['camera'] = demo_camera_pose_transformation(inference)
            success_count += 1
            print("✅ Camera pose demo completed")
        except Exception as e:
            print(f"❌ Camera pose demo failed: {e}")
    
    if (args.demo_6dof or args.demo_all) and args.enable_6dof:
        try:
            demo_results['6dof_eval'] = demo_6dof_evaluation(inference)
            if demo_results['6dof_eval'] is not None:
                success_count += 1
                print("✅ 6DOF evaluation demo completed")
            else:
                print("⚠️  6DOF evaluation demo skipped")
        except Exception as e:
            print(f"❌ 6DOF evaluation demo failed: {e}")
    
    if (args.demo_refinement or args.demo_all) and args.enable_refinement:
        try:
            demo_results['refinement'] = demo_6dof_refinement(inference)
            if demo_results['refinement'] is not None:
                success_count += 1
                print("✅ Grasp refinement demo completed")
            else:
                print("⚠️  Grasp refinement demo skipped")
        except Exception as e:
            print(f"❌ Grasp refinement demo failed: {e}")
    
    if args.demo_visualization or args.demo_all:
        try:
            # Use results from previous demos if available
            viz_results = demo_results.get('6dof_eval') or demo_results.get('basic')
            viz_success = demo_enhanced_visualization(inference, viz_results)
            if viz_success:
                success_count += 1
                print("✅ Enhanced visualization demo completed")
            else:
                print("⚠️  Enhanced visualization demo had issues")
        except Exception as e:
            print(f"❌ Enhanced visualization demo failed: {e}")
    
    if args.demo_animation or args.demo_all:
        try:
            anim_success = demo_diffusion_animation(inference)
            if anim_success:
                success_count += 1
                print("✅ Diffusion animation demo completed")
            else:
                print("⚠️  Diffusion animation demo failed")
        except Exception as e:
            print(f"❌ Diffusion animation demo failed: {e}")
    
    if args.demo_file_io or args.demo_all or args.pc_file:
        try:
            demo_results['file_io'] = demo_file_input_output(inference, args.pc_file)
            if demo_results['file_io'] is not None:
                success_count += 1
                print("✅ File I/O demo completed")
            else:
                print("⚠️  File I/O demo skipped")
        except Exception as e:
            print(f"❌ File I/O demo failed: {e}")
    
    if args.demo_comprehensive or args.demo_all:
        try:
            comp_success = demo_comprehensive_pipeline(inference)
            if comp_success:
                success_count += 1
                print("✅ Comprehensive pipeline demo completed")
            else:
                print("⚠️  Comprehensive pipeline demo failed")
        except Exception as e:
            print(f"❌ Comprehensive pipeline demo failed: {e}")
    
    # Final summary
    print(f"\n" + "="*80)
    print(f"📊 UNIFIED DEMO SUMMARY")
    print(f"="*80)
    print(f"Successfully completed demos: {success_count}")
    print(f"6DOF-GraspNet integration: {'✓ Active' if args.enable_6dof and SIXDOF_AVAILABLE else '✗ Disabled'}")
    print(f"Grasp refinement: {'✓ Active' if args.enable_refinement and SIXDOF_AVAILABLE else '✗ Disabled'}")
    
    # List generated files
    import glob
    demo_files = glob.glob("demo_*.glb") + glob.glob("demo_*.gif") + glob.glob("demo_*.ply") + glob.glob("demo_*.obj")
    if demo_files:
        print(f"\n📁 Generated demo files:")
        for file in sorted(demo_files):
            if os.path.exists(file):
                size_mb = os.path.getsize(file) / (1024*1024)
                print(f"  {file} ({size_mb:.2f} MB)")
    
    return success_count > 0


# =============================================================================
# COMMAND LINE INTERFACE
# =============================================================================

def main():
    parser = argparse.ArgumentParser(
        description="GraspLDM Enhanced Inference - Unified Demo",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic demo with synthetic data
  python examples/simple_inference_example.py --exp_path checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k
  
  # Full demo with all features
  python examples/simple_inference_example.py --exp_path <exp_path> --demo_all --enable_6dof --enable_refinement
  
  # Demo with point cloud file
  python examples/simple_inference_example.py --exp_path <exp_path> --pc_file data/example.ply --enable_6dof
  
  # Test visualization only
  python examples/simple_inference_example.py --exp_path <exp_path> --test_visualization_only
        """
    )
    
    # Required arguments
    parser.add_argument("--exp_path", type=str, required=True,
                       help="Path to experiment directory containing model checkpoints")
    
    # Optional input files
    parser.add_argument("--pc_file", type=str, default=None,
                       help="Optional point cloud file to load (.ply, .pcd, .xyz, .txt)")
    
    # Device and performance settings
    parser.add_argument("--device", type=str, default="cuda:0" if torch.cuda.is_available() else "cpu",
                       help="Device for inference (default: auto-detect)")
    parser.add_argument("--fast_steps", type=int, default=100,
                       help="Number of inference steps for fast sampling (default: 100)")
    
    # 6DOF-GraspNet integration settings
    parser.add_argument("--enable_6dof", action="store_true",
                       help="Enable 6DOF-GraspNet evaluation")
    parser.add_argument("--enable_refinement", action="store_true",
                       help="Enable grasp refinement")
    parser.add_argument("--evaluator_model_path", type=str, default=None,
                       help="Path to 6DOF evaluator model")
    parser.add_argument("--refinement_method", type=str, default="gradient",
                       choices=["gradient", "sampling"],
                       help="Refinement method (default: gradient)")
    parser.add_argument("--refinement_steps", type=int, default=5,
                       help="Number of refinement iterations (default: 5)")
    parser.add_argument("--refinement_threshold", type=float, default=0.7,
                       help="Success probability threshold (default: 0.7)")
    parser.add_argument("--choose_fn", type=str, default="better_than_threshold",
                       choices=["all", "better_than_threshold", "better_than_threshold_in_sequence"],
                       help="Grasp selection strategy (default: better_than_threshold)")
    
    # Demonstration selection
    parser.add_argument("--demo_all", action="store_true",
                       help="Run all demonstrations")
    parser.add_argument("--demo_basic", action="store_true",
                       help="Run basic inference demo")
    parser.add_argument("--demo_camera", action="store_true",
                       help="Run camera pose transformation demo")
    parser.add_argument("--demo_6dof", action="store_true",
                       help="Run 6DOF evaluation demo")
    parser.add_argument("--demo_refinement", action="store_true",
                       help="Run grasp refinement demo")
    parser.add_argument("--demo_visualization", action="store_true",
                       help="Run enhanced visualization demo")
    parser.add_argument("--demo_animation", action="store_true",
                       help="Run diffusion animation demo")
    parser.add_argument("--demo_file_io", action="store_true",
                       help="Run file input/output demo")
    parser.add_argument("--demo_comprehensive", action="store_true",
                       help="Run comprehensive pipeline demo")
    
    # Special testing modes
    parser.add_argument("--test_visualization_only", action="store_true",
                       help="Test visualization methods only (no full inference)")
    parser.add_argument("--visualize", action="store_true",
                       help="Enable interactive 3D visualization during demos")
    
    args = parser.parse_args()
    
    # Validate arguments
    if not os.path.exists(args.exp_path):
        print(f"❌ Experiment path not found: {args.exp_path}")
        print("Please ensure you have downloaded the model checkpoints.")
        return 1
    
    if args.pc_file and not os.path.exists(args.pc_file):
        print(f"❌ Point cloud file not found: {args.pc_file}")
        return 1
    
    # Default to basic demo if no specific demos selected
    if not any([args.demo_all, args.demo_basic, args.demo_camera, args.demo_6dof, 
               args.demo_refinement, args.demo_visualization, args.demo_animation,
               args.demo_file_io, args.demo_comprehensive, args.test_visualization_only]):
        args.demo_basic = True
        print("ℹ️  No specific demo selected, defaulting to basic demo")
    
    # Ensure 6DOF features are available if requested
    if (args.enable_6dof or args.enable_refinement) and not SIXDOF_AVAILABLE:
        print("⚠️  6DOF-GraspNet features requested but not available")
        print("   6DOF evaluation and refinement will be disabled")
        args.enable_6dof = False
        args.enable_refinement = False
    
    # Suppress warnings for cleaner demo output
    warnings.filterwarnings("ignore", category=UserWarning)
    
    # Run unified demo
    try:
        success = run_unified_demo(args)
        if success:
            print("\n🎉 Unified demo completed successfully!")
            print("\nNext steps:")
            print("1. Check the generated demo files")
            print("2. Modify parameters to explore different configurations") 
            print("3. Use your own point cloud data with --pc_file")
            return 0
        else:
            print("\n⚠️  Demo completed with some issues")
            print("Check the output above for specific error details")
            return 1
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Demo interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Demo failed with unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
