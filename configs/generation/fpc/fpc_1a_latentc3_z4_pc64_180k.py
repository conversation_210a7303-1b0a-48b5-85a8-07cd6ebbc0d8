"""
Configuration file for GraspLDM training with Full Point Cloud (FPC) approach.

This configuration defines a complete training setup for grasp generation using:
- Latent channels: 3
- Latent dimensions: 4  
- Point cloud features: 64
- Training steps: 180k

The model combines a Conditional Variational Autoencoder (CVAE) with a 
Denoising Diffusion Model (DDM) for high-quality grasp pose generation.
"""

import os

## --------------------  Most frequently changed params here  --------------------

# Training resumption control
resume_training_from_last = True  # Resume from the last checkpoint if available

# Training schedule parameters
max_steps = 180000  # Total number of training steps (180k)
batch_size = 10     # Number of samples per batch

# Hardware configuration
num_gpus = 1                # Number of GPUs to use for training
num_workers_per_gpu = 7     # Number of data loading workers per GPU

# Checkpoint paths for model initialization
# During training, if a ckpt is provided here, it overrides resume_training_from_last and instead resumes training from this ckpt
vae_ckpt_path = None  # "output/boilerplate_kldanneal_c0.1/vae/checkpoints/last.ckpt"
ddm_ckpt_path = None

# Data limitation (None = use all available scenes)
max_scenes = None

# Root directory containing the ACRONYM dataset
root_data_dir = "data/ACRONYM"

## -------------------- Inputs/Shapes ------------------------
# Input/Output: grasp representation [mrp(3), t(3), cls_success(1), qualities(4)]

# Point cloud configuration
pc_num_points = 1024        # Number of points in each point cloud
pc_latent_dims = 64         # Dimensionality of point cloud latent representation
pc_latent_channels = 3      # Number of channels in point cloud latent space

# Grasp representation configuration
grasp_pose_dims = 6         # Grasp pose dimensions (3 for rotation MRP + 3 for translation)
num_output_qualities = 0    # Number of quality metrics to predict (0 = no quality prediction)
grasp_latent_dims = 4       # Dimensionality of grasp latent representation

# Calculate total grasp representation dimensions
# Includes pose, optional qualities, and success classification
grasp_representation_dims = (
    grasp_pose_dims + num_output_qualities + 1
    if num_output_qualities is not None
    else grasp_pose_dims + 1
)

## ----------------------- Model -----------------------

# Regularization parameter
dropout = 0.1  # Dropout rate for neural networks (or None to disable)

# Point cloud encoder configuration using PVCNN architecture
pc_encoder_config = dict(
    type="PVCNNEncoder",
    args=dict(
        in_features=3,                      # Input features (x, y, z coordinates)
        n_points=pc_num_points,             # Number of input points
        scale_channels=0.75,                # Channel scaling factor for network width
        scale_voxel_resolution=0.75,        # Voxel resolution scaling for efficiency
        num_blocks=(1, 1, 1, 1),           # Number of blocks in each stage
        out_channels=pc_latent_channels,    # Output channel dimensions
        use_global_attention=False,         # Disable global attention mechanism
    ),
)

# Grasp encoder configuration using 1D ResNet
grasp_encoder_config = dict(
    type="ResNet1D",
    args=dict(
        in_features=grasp_representation_dims,  # Input grasp representation size
        block_channels=(32, 64, 128, 256),      # Channel progression through blocks
        input_conditioning_dims=pc_latent_dims, # Point cloud conditioning dimensions
        resnet_block_groups=4,                  # Number of groups in ResNet blocks
        dropout=dropout,                        # Dropout rate
    ),
)

# Decoder configuration for grasp reconstruction
decoder_config = dict(
    type="ResNet1D",
    args=dict(
        block_channels=(32, 64, 128, 256),      # Channel progression through blocks
        # out_dim=grasp_pose_dims,              # Output dimensions (commented out)
        input_conditioning_dims=pc_latent_dims, # Point cloud conditioning dimensions
        resnet_block_groups=4,                  # Number of groups in ResNet blocks
        dropout=dropout,                        # Dropout rate
    ),
)

# Loss function configuration for VAE training
loss_config = dict(
    # Reconstruction loss for grasp pose accuracy
    reconstruction_loss=dict(
        type="GraspReconstructionLoss",
        name="reconstruction_loss",
        args=dict(
            translation_weight=1,  # Weight for translation component
            rotation_weight=1      # Weight for rotation component
        ),
    ),
    # KL divergence loss for latent space regularization
    latent_loss=dict(
        type="VAELatentLoss",
        args=dict(
            name="grasp_latent",
            cyclical_annealing=True,    # Use cyclical annealing schedule
            num_steps=max_steps,        # Total training steps for annealing
            num_cycles=1,               # Number of annealing cycles
            ratio=0.5,                  # Ratio of cycle spent increasing
            start=1e-7,                 # Starting KL weight
            stop=0.1,                   # Maximum KL weight
        ),
    ),
    # Classification loss for grasp success prediction
    classification_loss=dict(
        type="ClassificationLoss", 
        args=dict(weight=0.1)  # Weight for classification component
    ),
    # Quality loss (commented out - not used in this configuration)
    # quality_loss=dict(type="QualityLoss", args=dict(weight=0.1)),
)

# Denoising model configuration for diffusion process
denoiser_model = dict(
    type="TimeConditionedResNet1D",
    args=dict(
        dim=grasp_latent_dims,              # Input/output dimensions
        channels=1,                         # Number of input channels
        block_channels=(32, 64, 128, 256),  # Channel progression through blocks
        input_conditioning_dims=pc_latent_dims,  # Point cloud conditioning dimensions
        resnet_block_groups=4,              # Number of groups in ResNet blocks
        dropout=dropout,                    # Dropout rate
        is_time_conditioned=True,           # Enable time step conditioning
        learned_variance=False,             # Use fixed variance schedule
        learned_sinusoidal_cond=False,      # Use standard sinusoidal conditioning
        random_fourier_features=True,       # Enable random Fourier features for time encoding
        # learned_sinusoidal_dim=16,        # Sinusoidal conditioning dimensions (commented out)
    ),
)
# Model architecture definition
# Use `model` for single module to be built. If a list of modules are required to be built, use `models` to make sure the outer
# See models/builder.py for more info.
model = dict(
    # Variational Autoencoder configuration
    vae=dict(
        model=dict(
            type="GraspCVAE",  # Conditional Variational Autoencoder for grasps
            args=dict(
                grasp_latent_size=grasp_latent_dims,        # Grasp latent space size
                pc_latent_size=pc_latent_dims,              # Point cloud latent space size
                pc_encoder_config=pc_encoder_config,        # Point cloud encoder settings
                grasp_encoder_config=grasp_encoder_config,  # Grasp encoder settings
                decoder_config=decoder_config,              # Decoder settings
                loss_config=loss_config,                    # Loss function settings
                num_output_qualities=num_output_qualities,  # Number of quality outputs
                intermediate_feature_resolution=16,         # Resolution of intermediate features
            ),
        ),
        ckpt_path=vae_ckpt_path,  # Checkpoint path for VAE initialization
    ),
    # Denoising Diffusion Model configuration
    ddm=dict(
        model=dict(
            type="GraspLatentDDM",  # Latent Denoising Diffusion Model
            args=dict(
                model=denoiser_model,           # Denoising network configuration
                latent_in_features=grasp_latent_dims,  # Input latent dimensions
                diffusion_timesteps=1000,      # Number of diffusion time steps
                noise_scheduler_type="ddpm",   # DDPM noise scheduling
                diffusion_loss="l2",           # L2 loss for diffusion training
                beta_schedule="linear",        # Linear beta schedule
                is_conditioned=True,           # Enable conditioning on point clouds
                joint_training=False,          # Train DDM separately from VAE
                denoising_loss_weight=1,       # Weight for denoising loss
                variance_type="fixed_large",   # Use fixed large variance
                elucidated_diffusion=False,    # Disable elucidated diffusion
                beta_start=0.00005,            # Starting beta value
                beta_end=0.001,                # Ending beta value
            ),
        ),
        ckpt_path=ddm_ckpt_path,      # Checkpoint path for DDM initialization
        use_vae_ema_model=True,       # Use exponential moving average VAE model
    ),
)
## -- Data --
# Data augmentation configuration for training robustness
augs_config = [
    # Random rotation augmentation
    dict(
        type="RandomRotation", 
        args=dict(
            p=0.5,           # Probability of applying rotation
            max_angle=180,   # Maximum rotation angle
            is_degree=True   # Angle specified in degrees
        )
    ),
    # Point cloud jittering for noise robustness
    dict(
        type="PointcloudJitter", 
        args=dict(
            p=1,             # Probability of applying jitter (always applied)
            sigma=0.005,     # Standard deviation of Gaussian noise
            clip=0.005       # Clipping threshold for noise
        )
    ),
    # Random point dropout for occlusion robustness
    dict(
        type="RandomPointcloudDropout", 
        args=dict(
            p=0.5,                  # Probability of applying dropout
            max_dropout_ratio=0.4   # Maximum fraction of points to drop
        )
    ),
]

# Object categories included in training dataset
# Comprehensive list of everyday objects from ACRONYM dataset
object_categories = [
    "Cup",
    "Mug",
    "Fork",
    "Hat",
    "Bottle",
    "Bowl",
    "Car",
    "Donut",
    "Laptop",
    "MousePad",
    "Pencil",
    "Plate",
    "ScrewDriver",
    "WineBottle",
    "Backpack",
    "Bag",
    "Banana",
    "Battery",
    "BeanBag",
    "Bear",
    "Book",
    "Books",
    "Camera",
    "CerealBox",
    "Cookie",
    "Hammer",
    "Hanger",
    "Knife",
    "MilkCarton",
    "Painting",
    "PillBottle",
    "Plant",
    "PowerSocket",
    "PowerStrip",
    "PS3",
    "PSP",
    "Ring",
    "Scissors",
    "Shampoo",
    "Shoes",
    "Sheep",
    "Shower",
    "Sink",
    "SoapBottle",
    "SodaCan",
    "Spoon",
    "Statue",
    "Teacup",
    "Teapot",
    "ToiletPaper",
    "ToyFigure",
    "Wallet",
    "WineGlass",
    "Cow",
    "Sheep",
    "Cat",
    "Dog",
    "Pizza",
    "Elephant",
    "Donkey",
    "RubiksCube",
    "Tank",
    "Truck",
    "USBStick",
]

# Training dataset configuration
train_data = dict(
    type="AcronymShapenetPointclouds",  # Dataset type for ACRONYM ShapeNet point clouds
    args=dict(
        data_root_dir=root_data_dir,                    # Root directory of dataset
        batch_num_points_per_pc=pc_num_points,         # Points per point cloud in batch
        batch_num_grasps_per_pc=100,                   # Grasps per point cloud in batch
        rotation_repr="mrp",                           # Modified Rodrigues Parameters for rotation
        augs_config=augs_config,                       # Data augmentation configuration
        split="train",                                 # Use training split
        batch_failed_grasps_ratio=0,                   # Ratio of failed grasps (0 = only successful)
        use_dataset_statistics_for_norm=False,         # Don't use dataset statistics for normalization
        filter_categories=object_categories,           # Filter to specified object categories
        load_fixed_subset_grasps_per_obj=None,        # Load all available grasps per object
        num_repeat_dataset=10,                         # Repeat dataset 10 times per epoch
    ),
)

# Data configuration dictionary
data = dict(
    train=train_data,  # Training data configuration
)

# Mesh configuration for simulation (patch)
mesh_root = root_data_dir        # Root directory for mesh data
mesh_categories = object_categories  # Object categories for mesh loading

## --------------------  Trainer  --------------------

# Logging configuration
logger = dict(
    type="WandbLogger",           # Use Weights & Biases for experiment tracking
    project="full-pc-ema-63c"    # Project name in W&B
)

# Optimizer and learning rate scheduler configuration
optimizer = dict(
    initial_lr=0.001,  # Initial learning rate
    scheduler=dict(
        type="MultiStepLR",  # Multi-step learning rate scheduler
        args=dict(
            milestones=[int(max_steps / 3), int(2 * max_steps / 3)],  # LR decay at 1/3 and 2/3 of training
            gamma=0.1  # Multiply LR by 0.1 at each milestone
        ),
    ),
)

# PyTorch Lightning trainer configuration
trainer = dict(
    max_steps=max_steps,                        # Maximum number of training steps
    batch_size=batch_size,                      # Batch size for training
    num_workers=num_workers_per_gpu * num_gpus, # Total number of data loading workers
    accelerator="gpu",                          # Use GPU acceleration
    devices=num_gpus,                           # Number of GPU devices
    strategy="ddp",                             # Distributed Data Parallel strategy
    logger=logger,                              # Logger configuration
    log_every_n_steps=100,                      # Log metrics every 100 steps
    optimizer=optimizer,                        # Optimizer configuration
    resume_training_from_last=resume_training_from_last,  # Resume from last checkpoint
    check_val_every_n_epoch=1,                 # Validate every epoch
    ema=dict(                                   # Exponential Moving Average configuration
        beta=0.990,                             # EMA decay factor
        update_after_step=1000,                 # Start EMA after 1000 steps
    ),
    deterministic=True,                         # Use deterministic algorithms for reproducibility
)
