"""
Enhanced GraspLDM Inference Interface with 6DOF-GraspNet Integration

This module provides a streamlined inference interface for GraspLDM that bypasses
the complex dataset loading pipeline and accepts raw point cloud data directly.
It also integrates 6DOF-GraspNet's grasp evaluation and refinement capabilities.

CRITICAL: Simplified Coordinate System Handling
===============================================

This implementation uses a simplified coordinate system handling approach between
GraspLDM and 6DOF-GraspNet for improved efficiency and clarity:

1. **Simplified Integration Flow**:
   - GraspLDM generates grasps in original (denormalized) coordinate system
   - 6DOF-GraspNet receives denormalized point cloud from GraspLDM results
   - Point cloud centering is performed once during 6DOF processing
   - Same centering transformation is applied to both point cloud and grasps

2. **Key Improvements**:
   - Eliminated redundant denormalization steps in point cloud processing
   - Single centering operation for both point cloud and grasp coordinates
   - Consistent coordinate transformations throughout the pipeline
   - Reduced complexity while maintaining spatial relationship accuracy

3. **Coordinate Transform Pipeline**:
   - Input: Denormalized point cloud and grasps from GraspLDM
   - Center point cloud: pc_centered = pc - pc.mean()
   - Center grasps: grasps_centered[..., :3, 3] -= pc_mean
   - 6DOF processing: operates in centered coordinate system
   - Output: grasps_final[..., :3, 3] += pc_mean (back to original coordinates)

This simplified approach maintains full compatibility with both GraspLDM and
6DOF-GraspNet while reducing computational overhead and coordinate transform complexity.

Key Features:
- Direct point cloud input (numpy arrays or torch tensors)
- Camera extrinsic parameter support
- Same preprocessing pipeline as original implementation
- Compatible with existing model weights and configurations
- Integrated 6DOF-GraspNet grasp evaluator for success probability scoring
- Grasp refinement using gradient-based and sampling-based optimization
- Suitable for real-time robotic applications

Usage Example:
    Basic inference:
    >>> inference = SimpleGraspLDMInference(exp_path="path/to/experiment")
    >>> results = inference.infer_from_pointcloud(pointcloud_array, num_grasps=20)

    With 6DOF-GraspNet evaluation and refinement:
    >>> inference = SimpleGraspLDMInference(
    ...     exp_path="path/to/experiment",
    ...     enable_grasp_evaluation=True,
    ...     enable_grasp_refinement=True,
    ...     evaluator_model_path="path/to/sixdof_evaluator.pth"
    ... )
    >>> results = inference.infer_from_pointcloud(
    ...     pointcloud=pointcloud_array,
    ...     num_grasps=20,
    ...     refine_grasps=True,
    ...     refinement_steps=5
    ... )

    With visualization and saving:
    >>> results = inference.infer_from_pointcloud(
    ...     pointcloud=pointcloud_array,
    ...     num_grasps=20,
    ...     visualize=True,
    ...     save_path="grasps_visualization.glb"  # Supports .glb, .ply, .obj, .stl
    ... )

    # Generate grasps from point cloud
    results = inference.infer_from_pointcloud(
        pointcloud=pc_array,  # [N, 3] numpy array or torch tensor
        camera_pose=cam_pose, # [4, 4] camera extrinsic matrix (optional)
        num_grasps=20,
        visualize=True
    )
"""

import os
import warnings
from typing import Union, Optional, Tuple, Dict, Any

import numpy as np
import torch
import torch.nn as nn

from grasp_ldm.models.builder import build_model_from_cfg
from grasp_ldm.utils.config import Config
from grasp_ldm.utils.rotations import tmrp_to_H, H_to_tmrp
from grasp_ldm.utils.pointcloud_helpers import PointCloudHelpers
from grasp_ldm.utils.vis import visualize_pc_grasps
from tools.inference import Experiment, fix_state_dict_prefix, unnormalize_grasps, unnormalize_pc

# # Import 6DOF-Evaluator components for evaluation and refinement
try:
    import sys
    import os.path
    
    # Get the absolute path to the current file's directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Build absolute path to sixdof_evaluator directory
    sixdof_path = os.path.abspath(os.path.join(current_dir, '..', 'sixdof_evaluator'))
    
    # Verify the path exists before adding to sys.path
    if not os.path.exists(sixdof_path):
        raise ImportError(f"6DOF-Evaluator directory not found at: {sixdof_path}")
    
    # Check if models directory exists
    models_path = os.path.join(sixdof_path, 'models')
    if not os.path.exists(models_path):
        raise ImportError(f"6DOF-Evaluator models directory not found at: {models_path}")
    
    # Add the 6dof_evaluator directory to path
    if sixdof_path not in sys.path:
        sys.path.insert(0, sixdof_path)
    
    # Import 6DOF-GraspNet components
    from models import create_model as create_6dof_model
    from utils import utils as grasp_utils
    from utils.visualization_utils import *
    
    SIXDOF_AVAILABLE = True
    print(f"✓ 6DOF-Evaluator loaded successfully from: {sixdof_path}")
    
except ImportError as e:
    SIXDOF_AVAILABLE = False
    print(f"⚠️  6DOF-Evaluator import error: {e}")
    print("   Grasp evaluation and refinement features will be disabled")
except Exception as e:
    SIXDOF_AVAILABLE = False
    print(f"⚠️  Unexpected error loading 6DOF-Evaluator: {e}")
    print("   Grasp evaluation and refinement features will be disabled")


class GraspFormatConverter:
    """
    Utility class for converting between graspLDM and 6DOF-Evaluator data formats.

    Handles conversions between:
    - TMRP (Translation + Modified Rodrigues Parameters) ↔ Homogeneous matrices
    - graspLDM coordinate system ↔ 6DOF-Evaluator coordinate system
    - Simplified point cloud centering operations

    Simplified Coordinate System Notes:
    - GraspLDM operates in the original world coordinate system after unnormalization
    - 6DOF-Evaluator expects both point clouds and grasps to be centered at origin
    - This class provides streamlined centering operations that apply consistent
      transformations to both point clouds and grasps using the same centering mean
    - Eliminates redundant denormalization steps for improved efficiency
    """

    @staticmethod
    def tmrp_to_6dof_format(tmrp_grasps: torch.Tensor) -> torch.Tensor:
        """
        Convert TMRP format grasps to 6DOF-Evaluator quaternion+translation format.

        Args:
            tmrp_grasps: [N, 6] TMRP format (translation + MRP)

        Returns:
            torch.Tensor: [N, 7] quaternion+translation format (qw, qx, qy, qz, tx, ty, tz)
        """
        # Convert TMRP to homogeneous matrices
        H_matrices = tmrp_to_H(tmrp_grasps)  # [N, 4, 4]

        # Extract rotation matrices and translations
        R = H_matrices[..., :3, :3]  # [N, 3, 3]
        t = H_matrices[..., :3, 3]   # [N, 3]

        # Convert rotation matrices to quaternions (wxyz format)
        from grasp_ldm.utils.rotations import rotmat_to_quat
        quaternions = rotmat_to_quat(R, return_wxyz=True)  # [N, 4] (w, x, y, z)

        # Concatenate quaternions and translations
        qt_grasps = torch.cat([quaternions, t], dim=-1)  # [N, 7]

        return qt_grasps

    @staticmethod
    def homogeneous_to_6dof_format(H_grasps: torch.Tensor) -> torch.Tensor:
        """
        Convert homogeneous transformation matrices to 6DOF-Evaluator format.

        Args:
            H_grasps: [N, 4, 4] homogeneous transformation matrices

        Returns:
            torch.Tensor: [N, 7] quaternion+translation format
        """
        # Extract rotation matrices and translations
        R = H_grasps[..., :3, :3]  # [N, 3, 3]
        t = H_grasps[..., :3, 3]   # [N, 3]

        # Convert rotation matrices to quaternions (wxyz format)
        from grasp_ldm.utils.rotations import rotmat_to_quat
        quaternions = rotmat_to_quat(R, return_wxyz=True)  # [N, 4] (w, x, y, z)

        # Concatenate quaternions and translations
        qt_grasps = torch.cat([quaternions, t], dim=-1)  # [N, 7]

        return qt_grasps

    @staticmethod
    def prepare_pc_for_6dof(pc: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Prepare point cloud for 6DOF-Evaluator processing with simplified centering operation.

        Args:
            pc: [N, 3] point cloud (already denormalized from graspLDM results)

        Returns:
            tuple: (pc_centered, pc_mean)
                - pc_centered: [N, 3] point cloud centered at origin
                - pc_mean: [3] centroid used for centering (needed for grasp coordinate transforms)
        """
        # Center point cloud at origin (consistent with GraspEstimator.prepare_pc)
        pc_mean = pc.mean(dim=0)  # [3]
        pc_centered = pc - pc_mean

        return pc_centered, pc_mean


class SimpleGraspLDMInference:
    """
    Simplified GraspLDM inference interface for direct point cloud input.

    This class provides a streamlined interface that bypasses the complex ACRONYM
    dataset structure and accepts raw point cloud data directly from cameras or
    other sources. It maintains full compatibility with existing model weights
    while offering a more flexible API for real-time applications.

    Key Capabilities:
    - Direct point cloud input (no dataset required)
    - Camera pose transformation support
    - Automatic point cloud preprocessing (centering, normalization)
    - Point cloud size automatically configured from trained model
    - Multiple conditioning modes (unconditional, class, region)
    - Built-in visualization support
    - Same output format as original inference pipeline

    Dimension Flow:
    - Input PC: [N, 3] raw coordinates → [1024, 3] regularized
    - Preprocessing: centering + normalization → [1024, 3] standardized
    - LDM Inference: [1024, 3] → [num_grasps, 4, 4] transformation matrices
    """

    def __init__(
        self,
        exp_path: str,                          # Path to experiment directory
        device: str = "cuda:0",                 # Compute device
        use_ema_model: bool = False,             # Use EMA model weights
        use_fast_sampler: bool = True,          # Enable fast sampling (DDIM)
        num_inference_steps: Optional[int] = 100, # Number of denoising steps
        # 6DOF-GraspNet Integration Parameters
        enable_grasp_evaluation: bool = False,  # Enable 6DOF-GraspNet evaluation
        enable_grasp_refinement: bool = False,  # Enable grasp refinement
        evaluator_model_path: Optional[str] = None, # Path to 6DOF evaluator model
        refinement_method: str = "gradient",    # Refinement method: 'gradient' or 'sampling'
        refinement_steps: int = 5,              # Number of refinement iterations
        refinement_threshold: float = 0.7,      # Success probability threshold
        # Grasp Selection Strategy Parameters
        choose_fn: str = "better_than_threshold", # Grasp selection strategy
    ):
        """
        Initialize the enhanced inference interface with 6DOF-Evaluator integration.

        Args:
            exp_path: Path to experiment directory containing model checkpoints
            device: PyTorch device for computation
            use_ema_model: Whether to use Exponential Moving Average weights
            use_fast_sampler: Enable fast sampling with DDIM scheduler
            num_inference_steps: Number of denoising steps for diffusion
            enable_grasp_evaluation: Enable 6DOF-Evaluator grasp evaluation
            enable_grasp_refinement: Enable grasp refinement capabilities
            evaluator_model_path: Path to 6DOF-Evaluator evaluator model
            refinement_method: Refinement method ('gradient' or 'sampling')
            refinement_steps: Number of refinement iterations
            refinement_threshold: Success probability threshold for grasp selection
            choose_fn: Grasp selection strategy ('all', 'better_than_threshold', 'better_than_threshold_in_sequence')
        """
        self.device = torch.device(device)
        self.use_ema_model = use_ema_model

        # Store 6DOF-Evaluator integration parameters
        self.enable_grasp_evaluation = enable_grasp_evaluation and SIXDOF_AVAILABLE
        self.enable_grasp_refinement = enable_grasp_refinement and SIXDOF_AVAILABLE
        self.evaluator_model_path = evaluator_model_path
        self.refinement_method = refinement_method
        self.refinement_steps = refinement_steps
        self.refinement_threshold = refinement_threshold

        # Grasp selection strategy configuration (matching grasp_estimator.py)
        self.choose_fn = choose_fn
        self.choose_fns = {
            "all": None,  # Keep all generated grasps (no filtering)
            "better_than_threshold": None,  # Will be set to utils function if available
            "better_than_threshold_in_sequence": None,  # Will be set to utils function if available
        }
        
        # Initialize choose_fns with utils functions if 6DOF is available
        if SIXDOF_AVAILABLE:
            try:
                self.choose_fns["better_than_threshold"] = grasp_utils.choose_grasps_better_than_threshold
                self.choose_fns["better_than_threshold_in_sequence"] = grasp_utils.choose_grasps_better_than_threshold_in_sequence
            except AttributeError as e:
                print(f"⚠️  Some choose functions not available in utils: {e}")

        # Initialize format converter
        self.format_converter = GraspFormatConverter()

        # Initialize experiment handler
        self.experiment = Experiment(
            exp_name=os.path.basename(exp_path),
            exp_out_root=os.path.dirname(exp_path),
            modes=["ddm", "vae"]
        )

        # Load configuration
        self.config = self.experiment.get_config("ddm")
        
        # Get num_points from configuration to ensure consistency with trained model
        self.num_points = self.config.pc_num_points
        print(f"✓ Using point cloud size from config: {self.num_points}")

        # Setup diffusion sampler
        self._setup_ldm_sampler(num_inference_steps, use_fast_sampler)

        # Load model
        self.model = self._load_model()

        # Set normalization parameters (from dataset statistics)
        self._set_normalization_params()

        # Sigmoid for confidence scores
        self._sigmoid = nn.Sigmoid()

        # Initialize 6DOF-Evaluator components if enabled
        self.grasp_evaluator = None
        if self.enable_grasp_evaluation or self.enable_grasp_refinement:
            self._setup_6dof_components()

        print(f"✓ Enhanced SimpleGraspLDMInference initialized")
        print(f"  Device: {self.device}")
        print(f"  Model: {'EMA' if use_ema_model else 'Standard'}")
        print(f"  Sampler: {'Fast DDIM' if use_fast_sampler else 'Standard DDPM'}")
        print(f"  Inference steps: {self.num_inference_steps}")
        print(f"  Point cloud size: {self.num_points}")
        print(f"  6DOF Evaluation: {'✓ Enabled' if self.enable_grasp_evaluation else '✗ Disabled'}")
        print(f"  Grasp Refinement: {'✓ Enabled' if self.enable_grasp_refinement else '✗ Disabled'}")
        if self.enable_grasp_refinement:
            print(f"    Method: {self.refinement_method}")
            print(f"    Steps: {self.refinement_steps}")
            print(f"    Threshold: {self.refinement_threshold}")
            print(f"    Selection strategy: {self.choose_fn}")
            if self.choose_fns[self.choose_fn] is not None:
                print(f"    ✓ Selection function loaded from utils")
            else:
                print(f"    ⚠️  Selection function not available, will use fallback")

    def _setup_6dof_components(self):
        """Initialize 6DOF-Evaluator evaluator and related components."""
        if not SIXDOF_AVAILABLE:
            print("❌ 6DOF-Evaluator not available, skipping component setup")
            return

        try:
            # Create options object for the evaluator following 6DOF-GraspNet pattern
            class EvaluatorOptions:
                def __init__(self, evaluator_model_path):
                    self.arch = "evaluator"
                    self.model_scale = 1
                    self.pointnet_radius = 0.02
                    self.pointnet_nclusters = 128
                    self.gpu_ids = [0] if torch.cuda.is_available() else []
                    self.init_type = 'normal'
                    self.init_gain = 0.02
                    self.is_train = False  # Important: set to False for inference
                    self.continue_train = False

                    # Set checkpoint directory and model name for automatic loading
                    if evaluator_model_path and os.path.exists(evaluator_model_path):
                        # Extract directory and epoch from path
                        self.checkpoints_dir = os.path.dirname(evaluator_model_path)
                        filename = os.path.basename(evaluator_model_path)
                        # Extract epoch from filename (e.g., "latest_net.pth" -> "latest")
                        self.which_epoch = filename.split('_net.pth')[0] if '_net.pth' in filename else 'latest'
                        self.name = os.path.basename(self.checkpoints_dir)
                    else:
                        # Use default pretrained model path
                        self.checkpoints_dir = os.path.join(os.path.dirname(__file__), '../sixdof_evaluator/checkpoints')
                        self.name = 'evaluator_pretrained'
                        self.which_epoch = 'latest'

            evaluator_opt = EvaluatorOptions(self.evaluator_model_path)

            # Create the evaluator model - this will automatically load weights if available
            self.grasp_evaluator = create_6dof_model(evaluator_opt)

            print(f"  ✓ 6DOF-Evaluator evaluator initialized")
            if self.evaluator_model_path:
                print(f"    Model path: {self.evaluator_model_path}")
            else:
                print(f"    Using default pretrained model")

        except Exception as e:
            print(f"  ❌ Failed to initialize 6DOF components: {e}")
            import traceback
            traceback.print_exc()
            self.enable_grasp_evaluation = False
            self.enable_grasp_refinement = False
            self.grasp_evaluator = None

    def _setup_ldm_sampler(self, num_inference_steps: Optional[int], use_fast_sampler: bool):
        """Configure the diffusion sampler parameters."""
        if use_fast_sampler:
            self.config.models.ddm.model.args.noise_scheduler_type = "ddim"
            self.fast_sampler = "DDIM"
            self.num_inference_steps = 100 if num_inference_steps is None else num_inference_steps
        else:
            self.fast_sampler = None
            self.num_inference_steps = 1000 if num_inference_steps is None else num_inference_steps

    def _load_model(self):
        """Load the trained LDM model with VAE."""
        # Build model from configuration
        model = build_model_from_cfg(self.config.model.ddm)
        model.set_vae_model(build_model_from_cfg(self.config.model.vae))

        # Load checkpoint
        ckpt_path = self.experiment.get_ckpt_path("ddm")
        state_dict = torch.load(ckpt_path, map_location=self.device)["state_dict"]
        
        # Use appropriate model prefix (EMA vs standard)
        model_prefix = "model" if not self.use_ema_model else "ema_model.online_model"
        state_dict = fix_state_dict_prefix(state_dict, model_prefix, ignore_all_others=True)

        # Load weights
        missing_keys, unexpected_keys = model.load_state_dict(state_dict, strict=True)
        if missing_keys:
            warnings.warn(f"Missing keys while loading state dict: {missing_keys}")
        if unexpected_keys:
            warnings.warn(f"Found unexpected keys while loading state dict: {unexpected_keys}")

        return model.eval().to(self.device)

    def _set_normalization_params(self):
        """
        Set normalization parameters based on dataset statistics.

        These parameters are derived from the training data and must match
        exactly for proper model performance. The values are based on the
        AcronymPartialPointclouds dataset preprocessing pipeline.
        """
        # Point cloud normalization (zero-centered after per-object centering)
        self._INPUT_PC_SHIFT = torch.zeros((3,), device=self.device)
        self._INPUT_PC_SCALE = torch.ones((3,), device=self.device) * 0.05  # translation_scale

        # Grasp normalization (zero-centered after per-object centering)
        self._INPUT_GRASP_SHIFT = torch.zeros((6,), device=self.device)
        self._INPUT_GRASP_SCALE = torch.cat([
            torch.ones((3,), device=self.device) * 0.05,  # translation_scale
            torch.ones((3,), device=self.device) * 0.5,   # rotation_scale
        ])

        print(f"✓ Normalization parameters set:")
        print(f"  PC scale: {self._INPUT_PC_SCALE[0].item():.3f}")
        print(f"  Grasp translation scale: {self._INPUT_GRASP_SCALE[0].item():.3f}")
        print(f"  Grasp rotation scale: {self._INPUT_GRASP_SCALE[3].item():.3f}")

    def _prepare_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None
    ) -> torch.Tensor:
        """
        Prepare raw point cloud for inference.

        This method performs the same preprocessing steps as the original
        AcronymPartialPointclouds dataset:
        1. Convert to torch tensor
        2. Apply camera pose transformation (if provided)
        3. Regularize point count to target size
        4. Convert to float32

        Args:
            pointcloud: Raw point cloud [N, 3] in camera or world coordinates
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation

        Returns:
            torch.Tensor: Prepared point cloud [num_points, 3]
        """
        # Convert to torch tensor
        if isinstance(pointcloud, np.ndarray):
            pc = torch.from_numpy(pointcloud).float()
        else:
            pc = pointcloud.float()

        # Ensure correct shape
        if pc.ndim != 2 or pc.shape[1] != 3:
            raise ValueError(f"Point cloud must have shape [N, 3], got {pc.shape}")

        # Apply camera pose transformation if provided
        if camera_pose is not None:
            pc = self._transform_pointcloud(pc, camera_pose)

        # Regularize point count
        pc = self._regularize_pointcloud(pc, self.num_points)

        return pc.to(self.device)

    def _transform_pointcloud(
        self,
        pointcloud: torch.Tensor,
        camera_pose: Union[np.ndarray, torch.Tensor]
    ) -> torch.Tensor:
        """
        Transform point cloud using camera extrinsic parameters.

        Args:
            pointcloud: Point cloud in camera coordinates [N, 3]
            camera_pose: Camera extrinsic matrix [4, 4] (camera to world transform)

        Returns:
            torch.Tensor: Transformed point cloud [N, 3]
        """
        if isinstance(camera_pose, np.ndarray):
            camera_pose = torch.from_numpy(camera_pose).float()

        # Ensure correct shape
        if camera_pose.shape != (4, 4):
            raise ValueError(f"Camera pose must be [4, 4] matrix, got {camera_pose.shape}")

        # Convert to homogeneous coordinates
        ones = torch.ones(pointcloud.shape[0], 1, dtype=pointcloud.dtype, device=pointcloud.device)
        pc_homogeneous = torch.cat([pointcloud, ones], dim=1)  # [N, 4]

        # Apply transformation
        pc_transformed = torch.matmul(pc_homogeneous, camera_pose.T)  # [N, 4]

        # Return to 3D coordinates
        return pc_transformed[:, :3]

    def _regularize_pointcloud(self, pointcloud: torch.Tensor, target_points: int) -> torch.Tensor:
        """
        Regularize point cloud to have exactly target_points points.

        This matches the behavior of PointCloudHelpers.regularize_pc_point_count
        used in the original dataset pipeline.

        Args:
            pointcloud: Input point cloud [N, 3]
            target_points: Target number of points

        Returns:
            torch.Tensor: Regularized point cloud [target_points, 3]
        """
        current_points = pointcloud.shape[0]

        if current_points < target_points:
            # Upsample: repeat existing points
            multiplier = max(target_points // current_points, 1)
            pc = pointcloud.repeat(multiplier, 1)

            # Add random points to reach exact target
            num_extra = target_points - pc.shape[0]
            if num_extra > 0:
                extra_indices = torch.randperm(pc.shape[0])[:num_extra]
                extra_points = pc[extra_indices]
                pc = torch.cat([pc, extra_points], dim=0)

        elif current_points > target_points:
            # Downsample: random selection
            indices = torch.randperm(current_points)[:target_points]
            pc = pointcloud[indices]
        else:
            pc = pointcloud

        return pc

    def _preprocess_pointcloud(self, pointcloud: torch.Tensor) -> Tuple[torch.Tensor, Dict[str, torch.Tensor]]:
        """
        Apply the same preprocessing pipeline as AcronymPartialPointclouds.

        This includes:
        1. Centering on point cloud mean
        2. Normalization using dataset statistics
        3. Metadata preparation for unnormalization

        Args:
            pointcloud: Prepared point cloud [num_points, 3]

        Returns:
            tuple: (preprocessed_pc, metadata_dict)
        """
        # Step 1: Center on point cloud mean
        pc_mean = torch.mean(pointcloud, dim=0)  # [3]
        pc_centered = pointcloud - pc_mean

        # Step 2: Normalize using dataset statistics
        pc_normalized = (pc_centered - self._INPUT_PC_SHIFT) / self._INPUT_PC_SCALE

        # Step 3: Prepare metadata for unnormalization
        grasp_mean = self._INPUT_GRASP_SHIFT.clone()
        grasp_mean[:3] += pc_mean  # Add centering offset to translation components

        metas = {
            "pc_mean": self._INPUT_PC_SHIFT + pc_mean,
            "pc_std": self._INPUT_PC_SCALE,
            "grasp_mean": grasp_mean,
            "grasp_std": self._INPUT_GRASP_SCALE,
            "dataset_normalized": True,
        }

        return pc_normalized, metas

    def _generate_grasps(
        self,
        pointcloud: torch.Tensor,
        metas: Dict[str, torch.Tensor],
        num_grasps: int = 20,
        return_intermediate: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        Generate grasps using the LDM model.

        This method implements the core LDM inference pipeline:
        1. Point cloud encoding to latent space
        2. Diffusion sampling in grasp latent space
        3. VAE decoding to grasp poses
        4. Post-processing and unnormalization

        Args:
            pointcloud: Preprocessed point cloud [num_points, 3]
            metas: Metadata dictionary for unnormalization
            num_grasps: Number of grasps to generate
            return_intermediate: Whether to return intermediate diffusion steps

        Returns:
            dict: Generated results containing:
                - grasps: [num_grasps, 4, 4] homogeneous transformation matrices
                - confidence: [num_grasps, 1] success probabilities
                - pc: [num_points, 3] unnormalized point cloud
                - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
        """
        # Ensure batch dimension
        batch_pc = pointcloud.unsqueeze(0)  # [1, num_points, 3]

        # Move metadata to device
        metas = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v
                for k, v in metas.items()}

        # Prepare model input
        in_kwargs = {"xyz": batch_pc, "metas": metas}

        # Configure fast sampling if enabled
        if self.fast_sampler == "DDIM":
            self.model.set_inference_timesteps(self.num_inference_steps)

        # Execute LDM inference with intermediate step control
        with torch.no_grad():
            final_grasps, all_diffusion_grasps = self.model.generate_grasps(
                num_grasps=num_grasps, 
                return_intermediate=return_intermediate, 
                **in_kwargs
            )

        # Parse model outputs
        if self.model.vae_model.decoder._use_qualities:
            tmrp, cls_logit, qualities = final_grasps
        else:
            tmrp, cls_logit = final_grasps
            qualities = None

        # Reshape to proper dimensions
        tmrp = tmrp.view(1, num_grasps, tmrp.shape[-1])

        # Unnormalize grasp poses
        grasp_unnorm = unnormalize_grasps(tmrp, metas)

        # Convert to homogeneous transformation matrices
        H_grasps = tmrp_to_H(grasp_unnorm)  # [1, num_grasps, 4, 4]

        # Process intermediate diffusion steps if available
        all_steps_grasps = []
        if return_intermediate and all_diffusion_grasps:
            # Note: For batched inference (batch_size > 1), intermediate steps are not supported
            # This follows the same limitation as InferenceLDM
            if batch_pc.shape[0] > 1:
                print("⚠️  Warning: Batched inference with intermediate steps not supported. Skipping intermediate results.")
            else:
                for step_grasp in all_diffusion_grasps:
                    # Each step_grasp is (tmrp, cls_logit) or (tmrp, cls_logit, qualities)
                    step_tmrp = step_grasp[0]
                    # Reshape to ensure num_grasps dimension is consistently present
                    reshaped_step_tmrp = step_tmrp.view(1, num_grasps, -1)
                    step_grasp_unnorm = unnormalize_grasps(reshaped_step_tmrp, metas)
                    step_H_grasps = tmrp_to_H(step_grasp_unnorm)
                    all_steps_grasps.append(step_H_grasps.squeeze(0))  # Remove batch dimension

        # Compute confidence scores (This value is wrong, need to be fixed)
        confidence = cls_logit.view(1, num_grasps, cls_logit.shape[-1])
        confidence = self._sigmoid(confidence)
        
        # Unnormalize point cloud
        pc_unnorm = unnormalize_pc(batch_pc, metas)

        return {
            "grasps": H_grasps.squeeze(0),      # [num_grasps, 4, 4]
            "grasp_tmrp": grasp_unnorm.squeeze(0),  # [num_grasps, 6] translation + rotation MRP
            "confidence": confidence.squeeze(0), # [num_grasps, 1]
            "pc": pc_unnorm.squeeze(0),         # [num_points, 3]
            "qualities": qualities,              # Optional quality metrics
            "all_steps_grasps": all_steps_grasps, # List of intermediate diffusion steps
        }

    # ------------------------------------------------------------------
    # Spatial Filtering Utility (reused from GraspEstimator logic)
    # ------------------------------------------------------------------
    def _filter_spatial_outliers(self, results: Dict[str, torch.Tensor], threshold: float = 0.1):
        """Remove grasps whose fingertip-midpoint lies farther than *threshold* from object centroid.

        The implementation mirrors sixdof_evaluator.GraspEstimator.keep_inliers logic but operates
        in-place on the *results* dictionary produced by ``_generate_grasps``.

        Args:
            results: Dict returned by ``_generate_grasps``. Must contain keys ``'grasps'`` and ``'pc'``.
            threshold: Maximum allowed Euclidean distance (in meters).
        """
        if 'grasps' not in results or 'pc' not in results:
            raise ValueError("results dict must contain 'grasps' and 'pc' entries")

        grasps = results['grasps']  # Tensor [G,4,4]
        pc = results['pc']          # Tensor [N,3]
        device = grasps.device

        # Compute centroid of object point cloud (same frame as grasps)
        obj_center = pc.mean(dim=0, keepdim=True).to(device)  # [1,3]

        # Convert to quaternion+translation for get_inlier_grasp_indices
        qt_grasps = self.format_converter.homogeneous_to_6dof_format(grasps)

        # get_inlier_grasp_indices expects list inputs and returns list of tuples from torch.where
        inlier_lists = grasp_utils.get_inlier_grasp_indices([qt_grasps], obj_center, threshold=threshold, device=device)
        
        # Extract inlier indices from the tuple returned by torch.where()
        # inlier_lists[0] is a tuple from torch.where(condition), we need the first element
        inlier_tuple = inlier_lists[0]  # This is a tuple: (indices_tensor,)
        
        # Following the original grasp_estimator.keep_inliers pattern:
        # grasps[i] = grasps[i][inlier_indices]
        # where inlier_indices is extracted from the torch.where tuple
        if isinstance(inlier_tuple, tuple) and len(inlier_tuple) > 0:
            inlier_indices = inlier_tuple[0]  # Extract the indices tensor from tuple
        else:
            # Fallback: if unexpected format, keep all grasps
            print("⚠️  Warning: Unexpected inlier format, keeping all grasps")
            inlier_indices = torch.arange(grasps.shape[0], device=device)

        # Apply filtering to relevant tensor entries that match the grasp dimension
        # This follows the exact pattern from grasp_estimator.keep_inliers
        for key, value in list(results.items()):
            if isinstance(value, torch.Tensor) and value.shape[0] == grasps.shape[0]:
                results[key] = value[inlier_indices]

    def infer_from_pointcloud(
        self,
        pointcloud: Union[np.ndarray, torch.Tensor],
        camera_pose: Optional[Union[np.ndarray, torch.Tensor]] = None,
        num_grasps: int = 10,
        return_intermediate: bool = False,
        visualize: bool = False,
        return_scene: bool = False,
        save_path: str = None,
        # 6DOF-GraspNet Integration Parameters
        evaluate_with_6dof: bool = None,        # Override evaluation setting
        refine_grasps: bool = None,             # Override refinement setting
        refinement_method: str = None,          # Override refinement method
        refinement_steps: int = None,           # Override refinement steps
        choose_fn: str = None,                  # Override grasp selection strategy
        # Refinement Process Visualization Parameters
        visualize_refinement_process: bool = False,   # Enable refinement process visualization
        track_refinement_process: bool = False,       # Enable refinement tracking for later visualization
        refinement_visualization_save_path: str = None  # Save path for refinement visualization
    ) -> Union[Dict[str, torch.Tensor], Any]:
        """
        Enhanced inference method with 6DOF-GraspNet evaluation and refinement.

        This is the primary interface for the enhanced inference pipeline.
        It accepts raw point cloud data and optional camera parameters, then
        performs the complete preprocessing, inference, evaluation, and refinement pipeline.

        Args:
            pointcloud: Raw point cloud [N, 3] as numpy array or torch tensor
            camera_pose: Optional camera extrinsic matrix [4, 4] for coordinate transformation
            num_grasps: Number of grasps to generate (default: 20)
            return_intermediate: Whether to return intermediate diffusion steps (default: False)
            visualize: Whether to display 3D visualization (default: False)
            return_scene: If True and visualize=True, return scene object instead of showing
            save_path: Optional path to save the visualization (supports .glb, .ply, .obj, .stl formats)
            evaluate_with_6dof: Override global evaluation setting for this call
            refine_grasps: Override global refinement setting for this call
            refinement_method: Override refinement method ('gradient' or 'sampling')
            refinement_steps: Override number of refinement steps
            choose_fn: Override grasp selection strategy ('all', 'better_than_threshold', 'better_than_threshold_in_sequence')
            visualize_refinement_process: If True, automatically visualize the refinement optimization process
            track_refinement_process: If True, enable tracking for later refinement visualization (stores data in results)
            refinement_visualization_save_path: Optional path to save refinement process visualization

        Returns:
            dict or Scene: If visualize=False, returns dict with:
                          - grasps: [num_grasps, 4, 4] transformation matrices
                          - grasp_tmrp: [num_grasps, 6] translation + rotation MRP
                          - confidence: [num_grasps, 1] success probabilities from graspLDM
                          - pc: [num_points, 3] processed point cloud
                          - all_steps_grasps: List of intermediate steps (if return_intermediate=True)
                          - sixdof_scores: [num_grasps] 6DOF-GraspNet evaluation scores (if enabled)
                          - refined_grasps: [num_grasps, 4, 4] refined grasp poses (if refinement enabled)
                          - refinement_history: Refinement process data (if refinement enabled)
                          If visualize=True and return_scene=True, returns 3D scene object
                          If visualize=True and return_scene=False, shows visualization and returns dict
        """
        # =====================================================================================
        # INITIALIZATION & PARAMETER SETUP
        # =====================================================================================
        
        # Dimension Convention for this function:
        # - N_raw = len(pointcloud) (raw input point count, variable)
        # - N = self.num_points (regularized point count from config, typically 1024)
        # - G = num_grasps (requested number of grasp poses to generate)
        # - G_post = variable (grasp count after spatial filtering, G_post <= G)
        # - K = variable (final grasp count after refinement selection, K <= G_post)
        # - S = refinement_steps + 2 (total tracking steps: initial + refinement + final)
        
        # Input validation: pointcloud expected to be [N_raw, 3]
        # camera_pose expected to be [4, 4] if provided
        
        # Determine 6DOF integration settings (allow per-call overrides)
        use_6dof_eval = evaluate_with_6dof if evaluate_with_6dof is not None else self.enable_grasp_evaluation
        use_refinement = refine_grasps if refine_grasps is not None else self.enable_grasp_refinement
        refine_method = refinement_method if refinement_method is not None else self.refinement_method
        refine_steps = refinement_steps if refinement_steps is not None else self.refinement_steps
        use_choose_fn = choose_fn if choose_fn is not None else self.choose_fn

        print(f"🚀 Starting enhanced grasp inference...")
        print(f"  Input PC shape: {pointcloud.shape}")  # Expected: [N_raw, 3]
        print(f"  Target grasps: {num_grasps}")
        print(f"  Camera pose: {'Yes' if camera_pose is not None else 'No'}")
        print(f"  Return intermediate: {'Yes' if return_intermediate else 'No'}")
        print(f"  6DOF Evaluation: {'Yes' if use_6dof_eval else 'No'}")
        print(f"  Grasp Refinement: {'Yes' if use_refinement else 'No'}")
        if use_refinement:
            print(f"    Method: {refine_method}, Steps: {refine_steps}")
            print(f"    Selection strategy: {use_choose_fn}")

        # =====================================================================================
        # STEP 1: POINT CLOUD PREPARATION - Format & Count Regularization
        # =====================================================================================
        
        # Transform: [N_raw, 3] → [N, 3] where N = self.num_points (typically 1024)
        # Operations: tensor conversion, camera transform (if provided), point count regularization
        pc_prepared = self._prepare_pointcloud(pointcloud, camera_pose)
        # pc_prepared: [N, 3] (torch.Tensor, on device, regularized point count)
        print(f"  ✓ Point cloud prepared: {pc_prepared.shape}")

        # =====================================================================================
        # STEP 2: POINT CLOUD PREPROCESSING - Centering & Normalization  
        # =====================================================================================
        
        # Transform: [N, 3] → [N, 3] (same shape, normalized values for LDM inference)
        # Operations: centering on point cloud mean, normalization using dataset statistics
        pc_preprocessed, metas = self._preprocess_pointcloud(pc_prepared)
        # pc_preprocessed: [N, 3] (normalized coordinates for LDM input)
        # metas: Dict with centering/scaling parameters for unnormalization
        print(f"  ✓ Preprocessing complete")

        # =====================================================================================
        # STEP 3: GRASP GENERATION - LDM Diffusion Inference
        # =====================================================================================
        
        # Transform: [N, 3] → Multiple output tensors
        # Primary operation: Latent diffusion model inference with VAE decoding
        results = self._generate_grasps(pc_preprocessed, metas, num_grasps, return_intermediate)
        # results['grasps']: [G, 4, 4] (homogeneous transformation matrices, unnormalized coordinates)
        # results['grasp_tmrp']: [G, 6] (translation + modified rodrigues parameters)  
        # results['confidence']: [G, 1] (graspLDM success probability estimates)
        # results['pc']: [N, 3] (unnormalized point cloud, matching grasp coordinate system)
        # results['all_steps_grasps']: List[Tensor[G, 4, 4]] (intermediate diffusion steps, if requested)
        print(f"  ✓ Generated {results['grasps'].shape[0]} grasps")

        # =====================================================================================
        # STEP 3.5: SPATIAL FILTERING - Remove Outlier Grasps
        # =====================================================================================
        
        # Transform: [G, 4, 4] → [G_post, 4, 4] where G_post <= G
        # Operations: Remove grasps whose fingertip locations are too far from object centroid
        try:
            pre_filter_count = results['grasps'].shape[0]  # G
            self._filter_spatial_outliers(results, threshold=0.1)
            post_filter_count = results['grasps'].shape[0]  # G_post
            # All tensors in results with first dimension G are now filtered to G_post
            # results['grasps']: [G_post, 4, 4] (spatially filtered grasps)
            # results['grasp_tmrp']: [G_post, 6] (spatially filtered)  
            # results['confidence']: [G_post, 1] (spatially filtered)
            if post_filter_count < pre_filter_count:
                print(f"  ✓ Spatial filtering removed {pre_filter_count - post_filter_count} outlier grasps")
        except Exception as e:
            print(f"⚠️  Spatial filtering skipped due to error: {e}")
            
        if return_intermediate and results.get('all_steps_grasps'):
            # all_steps_grasps: List[Tensor[G, 4, 4]] (intermediate steps, count = diffusion_steps)
            print(f"  ✓ Captured {len(results['all_steps_grasps'])} intermediate steps")

        # =====================================================================================
        # STEP 4: 6DOF-GRASPNET EVALUATION - Success Probability Scoring
        # =====================================================================================
        
        if use_6dof_eval:
            print(f"  🔍 Evaluating grasps with 6DOF-Evaluator...")
            # Input: results['pc'] [N, 3], results['grasps'] [G_post, 4, 4]
            # Transform: [G_post, 4, 4] → [G_post] (success probability scores)
            sixdof_scores = self._evaluate_grasps_with_6dof(
                results['pc'], results['grasps']
            )
            # sixdof_scores: [G_post] (6DOF-Evaluator success probabilities, range [0, 1])
            results['sixdof_scores'] = sixdof_scores
            print(f"  ✓ 6DOF evaluation complete (mean score: {sixdof_scores.mean():.3f})")

        # =====================================================================================
        # STEP 5: GRASP REFINEMENT - Iterative Pose Optimization  
        # =====================================================================================
        
        if use_refinement:
            print(f"  🔧 Refining grasps with {refine_method} method...")
            # Input: results['pc'] [N, 3], results['grasps'] [G_post, 4, 4]
            # Transform: [G_post, 4, 4] → [K, 4, 4] where K depends on selection strategy
            # Determine whether to track refinement process
            should_track = track_refinement_process or visualize_refinement_process
            
            refined_grasps, refined_scores, refinement_data = self._refine_grasps_with_6dof(
                results['pc'], results['grasps'], refine_method, refine_steps, use_choose_fn, should_track
            )
            # refined_grasps: [K, 4, 4] (optimized grasp poses, potentially reduced count)
            # refined_scores: [K] (final success probabilities for selected grasps)
            # Note: K varies by selection strategy:
            #   - "all": K = (refine_steps + 2) * G_post (all refinement steps)  
            #   - "better_than_threshold": K <= (refine_steps + 2) * G_post (above threshold)
            #   - others: varies based on specific selection criteria
            
            results['refined_grasps'] = refined_grasps
            results['refined_scores'] = refined_scores
            
            # Store refinement tracking data if available
            if refinement_data is not None:
                results['refinement_data'] = refinement_data
                print(f"  📊 Refinement tracking data stored for visualization")

            # Update main grasps with refined versions - this changes primary output dimensions
            # results['grasps']: [G_post, 4, 4] → [K, 4, 4] (updated to refined grasp poses)
            results['grasps'] = refined_grasps
            if 'sixdof_scores' not in results:
                # results['sixdof_scores']: [G_post] → [K] (updated to refined scores)
                results['sixdof_scores'] = refined_scores

            print(f"  ✓ Refinement complete (mean refined score: {refined_scores.mean():.3f})")
            
            # =========================================================================
            # STEP 5.5: AUTOMATIC REFINEMENT PROCESS VISUALIZATION (OPTIONAL)
            # =========================================================================
            
            if visualize_refinement_process and refinement_data is not None:
                print(f"  🎨 Creating refinement process visualization...")
                try:
                    self.visualize_refinement_process(
                        refinement_data=refinement_data,
                        save_path=refinement_visualization_save_path,
                        show_scene=not return_scene,  # Don't show if we're returning scene objects
                        return_scene=False,  # Don't return scene, just display/save
                        color_by_score_improvement=True,
                        step_transparency_fade=True,
                        highlight_selected_grasps=True
                    )
                    print(f"  ✅ Refinement process visualization complete")
                except Exception as e:
                    print(f"  ⚠️  Refinement process visualization failed: {e}")
            elif visualize_refinement_process and refinement_data is None:
                print(f"  ⚠️  Refinement process visualization requested but no tracking data available")
        
        # =====================================================================================
        # STEP 6: VISUALIZATION PIPELINE - 3D Scene Generation & Rendering
        # =====================================================================================
        
        if visualize:
            # Input tensor dimensions for visualization (final state):
            # results['pc']: [N, 3] (point cloud for visualization, unchanged)
            # results['grasps']: [K, 4, 4] (final grasp poses - may be refined or original)
            # results['confidence']: [K, 1] or [G_post, 1] (confidence scores)
            # results['sixdof_scores']: [K] or [G_post] (6DOF evaluation scores, if available)
            
            # Try enhanced 6DOF-Evaluator visualization first
            if SIXDOF_AVAILABLE:
                try:
                    # Enhanced visualization may apply additional filtering/subsampling for performance
                    # but preserves the essential spatial relationships and score information
                    scene = self._visualize_with_6dof_scene(
                        results=results,
                        save_path=save_path,
                        show_scene=not return_scene,
                        return_scene=return_scene,
                        # Use enhanced features for better visualization
                        visualize_diverse_grasps=True,  # Filter for diversity by default
                        min_separation_distance=0.03,   # 3cm minimum separation
                        plasma_coloring=False,          # Default to standard point cloud coloring
                        show_gripper_mesh=False         # Use line representation for performance
                    )
                    
                    if return_scene:
                        # Return trimesh.Scene object instead of results dict
                        return scene  # Type: trimesh.Scene
                        
                except Exception as e:
                    print(f"⚠️  Enhanced 6DOF-Evaluator visualization failed: {e}")
                    print(f"   Falling back to standard visualization...")
                    # Fallback to standard visualization
                    scene = self._visualize_results(results, return_scene, save_path)
                    if return_scene:
                        return scene  # Type: trimesh.Scene
            else:
                # Use standard visualization when 6DOF-Evaluator is not available
                print(f"  Using standard visualization (6DOF-Evaluator not available)")
                scene = self._visualize_results(results, return_scene, save_path)
                if return_scene:
                    return scene  # Type: trimesh.Scene
            # Visualization completed (either shown or scene object returned)

        # =====================================================================================
        # FINAL OUTPUT - Complete Results Dictionary
        # =====================================================================================
        
        print(f"✅ Inference complete!")
        
        # Final results dictionary structure with shape documentation:
        # results['grasps']: [K, 4, 4] (final grasp transformation matrices)
        #   - K = num_grasps (original) if no refinement
        #   - K = selected_count if refinement enabled (depends on selection strategy)
        #   - Each [4, 4] matrix is a homogeneous transformation in original world coordinates
        #
        # results['grasp_tmrp']: [K, 6] (translation + modified rodrigues parameters)
        #   - Maintained in sync with grasps tensor after filtering/refinement
        #   - Format: [tx, ty, tz, rx, ry, rz] where r* are Modified Rodrigues Parameters
        #
        # results['confidence']: also [G_post, 1] NOT [K, 1] Not involved in refinement (graspLDM confidence scores)
        #   - Original graspLDM network confidence estimates
        #   - Range approximately [0, 1] after sigmoid activation
        #
        # results['pc']: [N, 3] (processed point cloud)
        #   - N = self.num_points (typically 1024, from config)
        #   - Unnormalized coordinates matching grasp coordinate system
        #
        # Optional additional outputs (if enabled):
        # results['sixdof_scores']: [K] (6DOF-Evaluator success probabilities)
        #   - Only present if use_6dof_eval=True
        #   - Range [0, 1], higher values indicate better grasp quality
        #
        # results['refined_grasps']: [K, 4, 4] (refined grasp poses)
        #   - Only present if use_refinement=True
        #   - Same as results['grasps'] when refinement is enabled
        #
        # results['refined_scores']: [K] (refined success probabilities)
        #   - Only present if use_refinement=True
        #   - Final success scores after refinement process
        #
        # results['all_steps_grasps']: List[Tensor[G, 4, 4]] (intermediate diffusion steps)
        #   - Only present if return_intermediate=True
        #   - Each list element represents one denoising step
        #   - Count = num_inference_steps (diffusion timesteps)
        #   - Note: G (original grasp count) may differ from K (final count)
        
        return results  # Type: Dict[str, torch.Tensor]

    def _evaluate_grasps_with_6dof(
        self,
        pointcloud: torch.Tensor,
        grasps: torch.Tensor
    ) -> torch.Tensor:
        """
        Evaluate grasps using 6DOF-Evaluator evaluator.

        Args:
            pointcloud: [num_points, 3] denormalized point cloud (from graspLDM results)
            grasps: [num_grasps, 4, 4] homogeneous transformation matrices (in original coordinate system)

        Returns:
            torch.Tensor: [num_grasps] success probabilities from 6DOF evaluator
        """
        if not self.enable_grasp_evaluation or self.grasp_evaluator is None:
            print("⚠️  6DOF evaluation not available, returning dummy scores")
            return torch.ones(grasps.shape[0], device=self.device) * 0.5

        try:
            # Step 1: Center point cloud and get centering mean for consistent coordinate transforms
            pc_for_6dof, pc_mean = self.format_converter.prepare_pc_for_6dof(pointcloud)

            # Step 2: Transform grasps from original coordinate system to centered coordinate system
            # This matches GraspEstimator's expectation that grasps are in centered coordinates
            grasps_centered = grasps.clone()
            grasps_centered[..., :3, 3] -= pc_mean  # Subtract centroid from translation components

            # Step 3: Convert centered grasps to 6DOF format (quaternion + translation)
            qt_grasps = self.format_converter.homogeneous_to_6dof_format(grasps_centered)

            # Step 4: Generate gripper control points for each grasp using the correct function
            with torch.no_grad():
                # Convert quaternion+translation to euler angles and translations
                # as required by control_points_from_rot_and_trans
                grasp_eulers, grasp_translations = grasp_utils.convert_qt_to_rt(qt_grasps)

                # Generate control points using the correct 6DOF-Evaluator function
                grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                    grasp_eulers, grasp_translations, device=self.device
                )

                # Prepare batched point cloud (same pc replicated for each grasp)
                num_grasps = grasp_pcs.shape[0]
                pc_batch = pc_for_6dof.unsqueeze(0).repeat(num_grasps, 1, 1)  # [G, N, 3]

                # Evaluate grasps using 6DOF evaluator
                success_probs = self.grasp_evaluator.evaluate_grasps(pc_batch, grasp_pcs)

                return success_probs.squeeze(-1)

        except Exception as e:
            print(f"❌ Error in 6DOF evaluation: {e}")
            import traceback
            traceback.print_exc()
            return torch.ones(grasps.shape[0], device=self.device) * 0.5

    def _refine_grasps_with_6dof(
        self,
        pointcloud: torch.Tensor,
        grasps: torch.Tensor,
        method: str = "gradient",
        num_steps: int = 5,
        choose_fn: str = None,
        track_refinement_process: bool = False
    ) -> Tuple[torch.Tensor, torch.Tensor, Optional[Dict[str, Any]]]:
        """
        Refine grasps using 6DOF-Evaluator refinement methods with detailed tensor shape tracking.

        Tensor Shape Convention:
        - G = num_grasps (number of input grasps)
        - N = num_points (number of points in point cloud)  
        - S = num_steps + 1 (refinement steps plus initial step)
        - K = selected_grasps (number of grasps after selection strategy)

        Coordinate System Flow:
        1. Input: Original world coordinates (from graspLDM denormalized output)
        2. Processing: Centered coordinates (object centroid at origin)
        3. Output: Back to original world coordinates

        Args:
            pointcloud: [N, 3] denormalized point cloud (original world coordinates)
            grasps: [G, 4, 4] initial grasp poses (original world coordinates, homogeneous matrices)
            method: Refinement method ('gradient' or 'sampling')
            num_steps: Number of refinement iterations
            choose_fn: Grasp selection strategy (overrides instance setting if provided)

        Returns:
            tuple: (refined_grasps, success_probabilities, refinement_data)
                - refined_grasps: [K, 4, 4] refined grasp poses (original world coordinates)
                - success_probabilities: [K] final success probabilities for selected grasps
                - refinement_data: Dict containing tracking data if track_refinement_process=True, else None
        """
        if not self.enable_grasp_refinement or self.grasp_evaluator is None:
            print("⚠️  6DOF refinement not available, returning original grasps")
            success_probs = self._evaluate_grasps_with_6dof(pointcloud, grasps)
            return grasps, success_probs, None

        try:
            # =============================================================================
            # STEP 1: COORDINATE SYSTEM TRANSFORMATION - Original → Centered
            # =============================================================================
            
            # Prepare point cloud for 6DOF processing (center at origin)
            # Input: pointcloud [N, 3] (original world coordinates)
            # Output: pc_for_6dof [N, 3] (centered coordinates), pc_mean [3] (centroid offset)
            pc_for_6dof, pc_mean = self.format_converter.prepare_pc_for_6dof(pointcloud)
            
            # Extract grasp count and create batched point cloud for parallel processing
            num_grasps = grasps.shape[0]  # G
            # Replicate centered point cloud for each grasp: [N, 3] → [G, N, 3]
            pc_batch = pc_for_6dof.unsqueeze(0).repeat(num_grasps, 1, 1)  # [G, N, 3]

            # Transform grasps from original world coordinates to centered coordinates
            # Input: grasps [G, 4, 4] (original world coordinates)
            # Process: Subtract point cloud centroid from translation components only
            grasps_centered = grasps.clone()  # [G, 4, 4]
            grasps_centered[..., :3, 3] -= pc_mean  # [G, 4, 4] (centered coordinates)
            # Note: Only translation components (:3, 3) are modified, rotation stays the same

            # =============================================================================
            # STEP 2: FORMAT CONVERSION - Homogeneous → Quaternion+Translation
            # =============================================================================
            
            # Convert homogeneous transformation matrices to 6DOF format
            # Input: grasps_centered [G, 4, 4] (centered coordinates, homogeneous matrices)
            # Output: qt_grasps [G, 7] (centered coordinates, [qw, qx, qy, qz, tx, ty, tz])
            qt_grasps = self.format_converter.homogeneous_to_6dof_format(grasps_centered)

            # =============================================================================
            # STEP 3: FORMAT CONVERSION - Quaternion+Translation → Euler+Translation  
            # =============================================================================
            
            # Convert to euler angles and translations for refinement algorithm
            # Input: qt_grasps [G, 7] (quaternion+translation format)
            # Output: grasp_eulers [G, 3] (euler angles), grasp_translations [G, 3] (translations)
            grasp_eulers, grasp_translations = grasp_utils.convert_qt_to_rt(qt_grasps)

            # =============================================================================
            # STEP 4: REFINEMENT METHOD SETUP
            # =============================================================================
            
            # Configure refinement method and enable gradients if needed
            if method == "gradient":
                # Gradient-based refinement: wrap tensors with requires_grad=True
                # Shape preserved: [G, 3] → [G, 3] (with gradients enabled)
                grasp_eulers = torch.autograd.Variable(
                    grasp_eulers.to(self.device), requires_grad=True
                )  # [G, 3] (gradient-enabled)
                grasp_translations = torch.autograd.Variable(
                    grasp_translations.to(self.device), requires_grad=True
                )  # [G, 3] (gradient-enabled)
                improve_fun = self._improve_grasps_gradient_based
            else:
                # Sampling-based refinement: no gradients needed
                # Shape preserved: [G, 3] → [G, 3] (standard tensors)
                improve_fun = self._improve_grasps_sampling_based

            # =============================================================================
            # STEP 5: ITERATIVE REFINEMENT TRACKING
            # =============================================================================
            
            # Initialize tracking lists for refinement history
            # Each list will contain S entries (initial + num_steps refinements)
            improved_success = []  # Will contain S × [G] arrays
            improved_eulers = []   # Will contain S × [G, 3] arrays  
            improved_ts = []       # Will contain S × [G, 3] arrays

            # Store initial poses (step 0)
            # Convert from torch tensors to numpy for consistent storage
            improved_eulers.append(grasp_eulers.cpu().data.numpy())    # [G, 3]
            improved_ts.append(grasp_translations.cpu().data.numpy())  # [G, 3]

            last_success = None  # Placeholder for sampling-based method state

            # =============================================================================
            # STEP 6: ITERATIVE REFINEMENT LOOP
            # =============================================================================
            
            # Execute refinement iterations
            for i in range(num_steps):
                # Apply one refinement step using selected method
                # Input: pc_batch [G, N, 3], grasp_eulers [G, 3], grasp_translations [G, 3]
                # Output: success_prob [G], updated last_success state
                success_prob, last_success = improve_fun(
                    pc_batch, grasp_eulers, grasp_translations, last_success
                )
                
                # Store refinement results for this step
                improved_success.append(success_prob.cpu().data.numpy())    # [G]
                improved_eulers.append(grasp_eulers.cpu().data.numpy())     # [G, 3]
                improved_ts.append(grasp_translations.cpu().data.numpy())   # [G, 3]
                # Note: grasp_eulers and grasp_translations are modified in-place by improve_fun

            # =============================================================================
            # STEP 7: FINAL EVALUATION
            # =============================================================================
            
            # Evaluate final refined grasps to complete the success tracking
            # Convert final euler+translation to control points for evaluation
            # Input: grasp_eulers [G, 3], grasp_translations [G, 3]
            # Output: grasp_pcs [G, gripper_points, 3]
            grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                grasp_eulers, grasp_translations, self.device
            )
            
            # Final evaluation and append to success history
            # Input: pc_batch [G, N, 3], grasp_pcs [G, gripper_points, 3]
            # Output: final_success_scores [G]
            final_evaluation = self.grasp_evaluator.evaluate_grasps(pc_batch, grasp_pcs)
            improved_success.append(final_evaluation.squeeze(-1).cpu().data.numpy())  # [G]

            # =============================================================================
            # STEP 8: REFINEMENT HISTORY CONSOLIDATION
            # =============================================================================
            
            # Convert refinement tracking lists to structured numpy arrays
            # List of S × [G, 3] → numpy array [S, G, 3]
            improved_eulers_np = np.asarray(improved_eulers)     # [S, G, 3] (euler angles over time)
            improved_ts_np = np.asarray(improved_ts)             # [S, G, 3] (translations over time)
            improved_success_np = np.asarray(improved_success)   # [S, G] (success scores over time)
            
            # Sanity check: S = num_steps + 1 (initial) + 1 (final evaluation) = num_steps + 2
            assert improved_eulers_np.shape[0] == num_steps + 1, f"Expected {num_steps + 1} refinement steps, got {improved_eulers_np.shape[0]}"

            # =============================================================================
            # STEP 9: GRASP SELECTION STRATEGY
            # =============================================================================
            
            # Apply grasp selection strategy to determine which grasps to keep
            active_choose_fn = choose_fn if choose_fn is not None else self.choose_fn
            
            if active_choose_fn == "all" or self.choose_fns[active_choose_fn] is None:
                # Keep all grasps from all refinement steps
                # Create selection mask: [S, G] with all ones
                selection_mask = np.ones(improved_eulers_np.shape[:2], dtype=np.float32)  # [S, G]
                print(f"  Using 'all' selection strategy (all refinement steps)")
            else:
                # Apply sophisticated selection strategy (e.g., better_than_threshold)
                # Input: improved_eulers_np [S, G, 3], improved_ts_np [S, G, 3], improved_success_np [S, G]
                # Output: selection_mask [S, G] (binary mask indicating selected grasps)
                selection_mask = self.choose_fns[active_choose_fn](
                    improved_eulers_np, improved_ts_np, improved_success_np, self.refinement_threshold
                )
                selected_count = np.sum(selection_mask)  # Total number of selected (step, grasp) pairs
                total_count = improved_success_np.size   # Total number of (step, grasp) pairs
                print(f"  Applied '{active_choose_fn}' selection: {selected_count}/{total_count} grasps selected")

            # =============================================================================
            # STEP 10: SELECTED GRASP CONVERSION - Euler+Translation → Homogeneous Matrices
            # =============================================================================
            
            # Convert selected (euler, translation) pairs back to homogeneous matrices
            # Input: improved_eulers_np [S, G, 3], improved_ts_np [S, G, 3], selection_mask [S, G]
            # Output: grasps_list List[K × [4, 4]] (K = number of selected grasps)
            grasps_list = grasp_utils.rot_and_trans_to_grasps(
                improved_eulers_np, improved_ts_np, selection_mask
            )
            
            # Stack selected grasps into single tensor (still in centered coordinates)
            # Input: grasps_list List[K × [4, 4]]
            # Output: refined_grasps_centered [K, 4, 4] (centered coordinates)
            refined_grasps_centered = torch.from_numpy(
                np.stack(grasps_list)  # Stack list of matrices into [K, 4, 4]
            ).to(self.device).float()

            # =============================================================================
            # STEP 11: SUCCESS PROBABILITY ALIGNMENT
            # =============================================================================
            
            # Extract success probabilities corresponding to selected grasps
            # Input: selection_mask [S, G], improved_success_np [S, G]
            # Process: Find (step_idx, grasp_idx) pairs where selection_mask == 1
            refine_indexes, sample_indexes = np.where(selection_mask)  # Two arrays of length K
            # Extract success scores for selected (step, grasp) pairs
            selected_success_np = improved_success_np[refine_indexes, sample_indexes]  # [K]
            # Convert to torch tensor
            refine_scores = torch.from_numpy(selected_success_np).to(self.device).float()  # [K]
             
            # =============================================================================
            # STEP 12: COORDINATE SYSTEM TRANSFORMATION - Centered → Original
            # =============================================================================
            
            # Transform refined grasps back to original world coordinate system
            # Input: refined_grasps_centered [K, 4, 4] (centered coordinates)
            # Process: Add point cloud centroid back to translation components
            refined_grasps = refined_grasps_centered.clone()  # [K, 4, 4]
            refined_grasps[..., :3, 3] += pc_mean  # [K, 4, 4] (original world coordinates)
            # Note: Only translation components (:3, 3) are modified, rotation stays the same
 
            # =============================================================================
            # RETURN FINAL RESULTS
            # =============================================================================
            
            # =============================================================================
            # STEP 13: COMPILE REFINEMENT TRACKING DATA (OPTIONAL)
            # =============================================================================
            
            refinement_data = None
            if track_refinement_process:
                # Prepare comprehensive tracking data for visualization
                refinement_data = {
                    # Core refinement history (numpy arrays for easy processing)
                    'improved_eulers': improved_eulers_np,      # [S, G, 3] euler angles over time
                    'improved_ts': improved_ts_np,              # [S, G, 3] translations over time  
                    'improved_success': improved_success_np,    # [S, G] success scores over time
                    'selection_mask': selection_mask,           # [S, G] binary selection mask
                    
                    # Coordinate system transformation data
                    'pc_mean': pc_mean.cpu().numpy(),           # [3] point cloud centroid offset
                    'pointcloud_centered': pc_for_6dof.cpu().numpy(),  # [N, 3] centered point cloud
                    'pointcloud_original': pointcloud.cpu().numpy(),   # [N, 3] original point cloud
                    
                    # Refinement configuration
                    'method': method,                           # refinement method used
                    'num_steps': num_steps,                     # number of refinement steps
                    'selection_strategy': active_choose_fn,     # grasp selection strategy
                    'threshold': self.refinement_threshold,     # success probability threshold
                    
                    # Selected grasp mapping (for visualization alignment)
                    'selected_step_indices': refine_indexes,    # [K] step indices of selected grasps
                    'selected_grasp_indices': sample_indexes,   # [K] grasp indices of selected grasps
                    
                    # Dimension information for validation
                    'num_grasps': num_grasps,                   # G: number of input grasps
                    'num_refinement_steps': num_steps + 1,      # S: refinement steps (initial + steps)
                    'num_selected_grasps': len(refine_indexes), # K: number of selected grasps
                }
                
                print(f"  📊 Refinement tracking data prepared:")
                print(f"    History shape: {improved_eulers_np.shape} (steps, grasps, dims)")
                print(f"    Selected grasps: {len(refine_indexes)} from {num_grasps * (num_steps + 1)} total")

            # =============================================================================
            # RETURN FINAL RESULTS WITH OPTIONAL TRACKING DATA
            # =============================================================================
            
            # Output shapes:
            # - refined_grasps: [K, 4, 4] (K selected grasps, original world coordinates)
            # - refine_scores: [K] (success probabilities for selected grasps)
            # - refinement_data: Dict with tracking data (if track_refinement_process=True)
            return refined_grasps, refine_scores, refinement_data

        except Exception as e:
            print(f"❌ Error in 6DOF refinement: {e}")
            success_probs = self._evaluate_grasps_with_6dof(pointcloud, grasps)
            return grasps, success_probs, None

    def _improve_grasps_gradient_based(self, pcs, grasp_eulers, grasp_trans, last_success):
        """
        Improve grasp poses using gradient-based optimization - exactly matching original.

        Uses backpropagation through the evaluator network to compute gradients
        of success probability w.r.t. grasp parameters, then updates poses
        in the direction of increasing success probability.

        Args:
            pcs: Point cloud batch
            grasp_eulers: Current euler angles (requires_grad=True)
            grasp_trans: Current translations (requires_grad=True)
            last_success: Previous success probabilities (unused)

        Returns:
            tuple: (success, None)
                - success: Current success probabilities
                - None: No carry-over state needed for gradient method
        """
        # Clear gradients before backpropagation 
        if grasp_eulers.grad is not None:
            grasp_eulers.grad.zero_()
        if grasp_trans.grad is not None:
            grasp_trans.grad.zero_()

        # Convert poses to gripper control points for evaluation
        grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
            grasp_eulers, grasp_trans, self.device
        )

        # Ensure batch dimension alignment
        pcs_batched = pcs if pcs.dim() == 3 else pcs.unsqueeze(0).repeat(grasp_pcs.shape[0], 1, 1)

        # Evaluate current grasp success probability
        success = self.grasp_evaluator.evaluate_grasps(pcs_batched, grasp_pcs)

        # Backpropagate to compute gradients
        success.squeeze(-1).backward(torch.ones(success.shape[0]).to(self.device))

        # Update translation with adaptive step size
        delta_t = grasp_trans.grad
        norm_t = torch.norm(delta_t, p=2, dim=-1).to(self.device)
        # Limit step size to maximum 1cm to ensure gradient validity
        alpha = torch.min(0.01 / norm_t, torch.tensor(1.0).to(self.device))
        grasp_trans.data += grasp_trans.grad * alpha[:, None]

        # Update euler angles with same adaptive step size
        grasp_eulers.data += grasp_eulers.grad * alpha[:, None]

        return success.squeeze(-1), None

    def _improve_grasps_sampling_based(
        self,
        pcs: torch.Tensor,  # [G, N, 3] - Batch of point clouds (G grasps, N points per cloud, 3 coordinates)
        grasp_eulers: torch.Tensor,  # [G, 3] - Euler angles (roll, pitch, yaw) for each grasp
        grasp_trans: torch.Tensor,  # [G, 3] - Translation vectors (x, y, z) for each grasp
        last_success: Optional[torch.Tensor] = None  # [G] - Success probabilities from previous iteration
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Improve grasp poses using sampling-based optimization - exactly matching original.

        Randomly perturbs current poses and accepts improvements probabilistically.
        Uses Metropolis-Hastings style acceptance criterion based on success ratio.

        Args:
            pcs: Point cloud batch
            grasp_eulers: Current euler angles
            grasp_trans: Current translations
            last_success: Previous success probabilities

        Returns:
            tuple: (last_success, next_success)
                - last_success: [G] - Previous success probabilities
                - next_success: [G] - Updated success probabilities after sampling step
        """
        with torch.no_grad():
            # Evaluate current poses if not provided
            if last_success is None:
                grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                    grasp_eulers, grasp_trans, self.device
                )
                pcs_batched = pcs if pcs.dim() == 3 else pcs.unsqueeze(0).repeat(grasp_pcs.shape[0], 1, 1)  # [G, N, 3]
                last_success = self.grasp_evaluator.evaluate_grasps(pcs_batched, grasp_pcs)  # [G]

            # Generate random perturbations
            # Translation perturbation: ±2cm range
            delta_t = 2 * (torch.rand(grasp_trans.shape).to(self.device) - 0.5)
            delta_t *= 0.02

            # Euler angle perturbation: ±π range
            delta_euler_angles = (torch.rand(grasp_eulers.shape).to(self.device) - 0.5) * 2

            # Apply perturbations
            perturbed_translation = grasp_trans + delta_t  # [G, 3]
            perturbed_euler_angles = grasp_eulers + delta_euler_angles  # [G, 3]

            # Evaluate perturbed poses
            grasp_pcs = grasp_utils.control_points_from_rot_and_trans(
                perturbed_euler_angles, perturbed_translation, self.device
            )
            pcs_batched = pcs if pcs.dim() == 3 else pcs.unsqueeze(0).repeat(grasp_pcs.shape[0], 1, 1)  # [G, N, 3]
            perturbed_success = self.grasp_evaluator.evaluate_grasps(pcs_batched, grasp_pcs)  # [G]

            # Compute acceptance probability (ratio of new to old success)
            ratio = perturbed_success / torch.max(
                last_success, torch.tensor(0.0001).to(self.device)  # Avoid division by zero
            )  # [G]

            # Accept improvements probabilistically (Metropolis-Hastings criterion)
            mask = torch.rand(ratio.shape).to(self.device) <= ratio  # [G] - Boolean mask for accepted samples

            # Update poses for accepted samples
            next_success = last_success  # [G]
            ind = torch.where(mask)[0]  # [M] - Indices of accepted samples (M <= G)
            next_success[ind] = perturbed_success[ind]  # Update success probabilities
            grasp_trans[ind].data = perturbed_translation.data[ind]  # Update translations
            grasp_eulers[ind].data = perturbed_euler_angles.data[ind]  # Update Euler angles

            return last_success.squeeze(-1), next_success  # Both [G]

    def _visualize_results(self, results: Dict[str, torch.Tensor], return_scene: bool = False, save_path: str = None):
        """
        Original visualization method using graspLDM's built-in visualizer.

        This method serves as a fallback when 6DOF-GraspNet enhanced visualization
        is not available or fails. It provides basic but reliable 3D scene rendering.

        Args:
            results: Results dictionary from inference
            return_scene: Whether to return scene object or show directly
            save_path: Optional path to save the visualization (supports .glb, .ply, .obj, .stl formats)

        Returns:
            Scene object if return_scene=True, otherwise None
        """
        # Convert to numpy for visualization
        pc_np = results["pc"].detach().cpu().numpy()
        grasps_np = results["grasps"].detach().cpu().numpy()
        confidence_np = results["confidence"].detach().cpu().numpy()

        # Create 3D scene
        scene = visualize_pc_grasps(pc_np, grasps_np, confidence_np)

        # Save scene if path provided
        if save_path is not None:
            try:
                scene.export(save_path)
                print(f"✅ Visualization saved to: {save_path}")
            except Exception as e:
                print(f"❌ Failed to save visualization: {e}")
            
        if return_scene:
            return scene
        else:
            scene.show(line_settings={"point_size": 10})
            return None

    def load_pointcloud_from_file(self, filepath: str) -> np.ndarray:
        """
        Load point cloud from common file formats.

        Supports .ply, .pcd, .xyz, and .txt files.

        Args:
            filepath: Path to point cloud file

        Returns:
            np.ndarray: Point cloud [N, 3]
        """
        import trimesh

        if filepath.endswith('.ply'):
            mesh = trimesh.load(filepath)
            if hasattr(mesh, 'vertices'):
                return np.array(mesh.vertices)
            else:
                raise ValueError("PLY file does not contain vertices")
        elif filepath.endswith('.pcd'):
            # Basic PCD file support
            import open3d as o3d
            pcd = o3d.io.read_point_cloud(filepath)
            return np.asarray(pcd.points)
        elif filepath.endswith(('.xyz', '.txt')):
            # Simple text format: x y z per line
            return np.loadtxt(filepath)[:, :3]
        else:
            raise ValueError(f"Unsupported file format: {filepath}")
    
    def filter_grasps_by_6dof_score(
        self,
        results: Dict[str, torch.Tensor],
        min_score: float = 0.5
    ) -> Dict[str, torch.Tensor]:
        """
        Filter generated grasps by 6DOF-GraspNet evaluation score.

        Args:
            results: Results dictionary from inference containing 'sixdof_scores'
            min_score: Minimum 6DOF evaluation score threshold (0.0 to 1.0)

        Returns:
            dict: Filtered results with same structure
        """
        if 'sixdof_scores' not in results:
            print("⚠️  No 6DOF scores available for filtering")
            return results

        scores = results["sixdof_scores"]
        mask = scores >= min_score

        if mask.sum() == 0:
            print(f"⚠️  No grasps above 6DOF score threshold {min_score:.2f}")
            return results

        filtered_results = {
            "grasps": results["grasps"][mask],
            "grasp_tmrp": results["grasp_tmrp"][mask],
            "confidence": results["confidence"][mask],
            "pc": results["pc"],  # Point cloud unchanged
            "sixdof_scores": results["sixdof_scores"][mask],
        }

        # Copy other optional fields if present
        for key in ["qualities", "refined_grasps", "refined_scores"]:
            if key in results and results[key] is not None:
                if isinstance(results[key], torch.Tensor) and results[key].dim() > 0:
                    filtered_results[key] = results[key][mask]
                else:
                    filtered_results[key] = results[key]

        print(f"✓ Filtered {mask.sum().item()}/{len(mask)} grasps above 6DOF score {min_score:.2f}")
        return filtered_results

    def get_best_grasps_by_6dof(
        self,
        results: Dict[str, torch.Tensor],
        top_k: int = 5
    ) -> Dict[str, torch.Tensor]:
        """
        Get top-k grasps by 6DOF-GraspNet evaluation score.

        Args:
            results: Results dictionary from inference containing 'sixdof_scores'
            top_k: Number of top grasps to return

        Returns:
            dict: Top-k results with same structure
        """
        if 'sixdof_scores' not in results:
            print("⚠️  No 6DOF scores available, falling back to confidence scores")
            return self.get_best_grasps(results, top_k)

        scores = results["sixdof_scores"]
        _, top_indices = torch.topk(scores, min(top_k, len(scores)))

        top_results = {
            "grasps": results["grasps"][top_indices],
            "grasp_tmrp": results["grasp_tmrp"][top_indices],
            "confidence": results["confidence"][top_indices],
            "pc": results["pc"],  # Point cloud unchanged
            "sixdof_scores": results["sixdof_scores"][top_indices],
        }

        # Copy other optional fields if present
        for key in ["qualities", "refined_grasps", "refined_scores"]:
            if key in results and results[key] is not None:
                if isinstance(results[key], torch.Tensor) and results[key].dim() > 0:
                    top_results[key] = results[key][top_indices]
                else:
                    top_results[key] = results[key]

        print(f"✓ Selected top-{len(top_indices)} grasps by 6DOF score")
        return top_results
    
    def save_visualization(self, results: Dict[str, torch.Tensor], save_path: str,
                         include_confidence_colors: bool = True) -> bool:
        """
        Save visualization of grasps to file with enhanced options.
        
        Args:
            results: Results dictionary from inference
            save_path: Path to save the visualization
            include_confidence_colors: Whether to color grasps by confidence
            
        Returns:
            bool: True if save was successful, False otherwise
        """
        try:
            # Convert to numpy for visualization
            pc_np = results["pc"].detach().cpu().numpy()
            grasps_np = results["grasps"].detach().cpu().numpy()
            confidence_np = results["confidence"].detach().cpu().numpy() if include_confidence_colors else None

            # Create 3D scene
            scene = visualize_pc_grasps(pc_np, grasps_np, confidence_np)
            
            # Ensure directory exists
            import os
            dir_path = os.path.dirname(save_path)
            if dir_path:  # Only create directory if path contains a directory
                os.makedirs(dir_path, exist_ok=True)
            
            # Export scene
            scene.export(save_path)
            
            # Validate file was created
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path)
                print(f"✅ Visualization saved to: {save_path} ({file_size} bytes)")
                return True
            else:
                print(f"❌ File was not created: {save_path}")
                return False
                
        except Exception as e:
            print(f"❌ Failed to save visualization: {e}")
            import traceback
            traceback.print_exc()
            return False

    def create_diffusion_animation(
        self,
        results: Dict[str, torch.Tensor],
        save_path: str = "diffusion_animation.gif",
        resolution: Tuple[int, int] = (800, 600),
        fps: int = 5,
        show_confidence_colors: bool = True,
        camera_distance: float = 0.8,
        show_progress: bool = True
    ) -> bool:
        """
        Create an animated visualization of the diffusion process evolution.
        
        This method generates a GIF animation showing how grasps evolve from noise
        to final poses during the diffusion denoising process. Each frame represents
        one denoising step, providing insight into the model's generation process.
        
        Args:
            results: Results dictionary from inference containing 'all_steps_grasps'
            save_path: Path to save the animation (supports .gif, .mp4)
            resolution: Output resolution as (width, height) tuple
            fps: Frames per second for the animation
            show_confidence_colors: Whether to color grasps by confidence (final step only)
            camera_distance: Camera distance from the scene center
            show_progress: Whether to display progress information
            
        Returns:
            bool: True if animation was created successfully, False otherwise
            
        Example:
            >>> # Generate grasps with intermediate steps
            >>> results = inference.infer_from_pointcloud(
            ...     pointcloud=pc_array,
            ...     num_grasps=10,
            ...     return_intermediate=True
            ... )
            >>> 
            >>> # Create animation
            >>> success = inference.create_diffusion_animation(
            ...     results=results,
            ...     save_path="diffusion_evolution.gif",
            ...     fps=3,
            ...     resolution=(1024, 768)
            ... )
        """
        # Check if intermediate steps are available
        if not results.get('all_steps_grasps') or len(results['all_steps_grasps']) == 0:
            print("❌ No intermediate steps found in results. Please run inference with return_intermediate=True")
            return False
        
        try:
            import trimesh
            from PIL import Image
            import numpy as np
            
            # Try to import imageio for better GIF/MP4 support
            try:
                import imageio
                use_imageio = True
            except ImportError:
                print("⚠️  imageio not found, using PIL for GIF creation (MP4 not supported)")
                use_imageio = False
                if save_path.endswith('.mp4'):
                    print("❌ MP4 output requires imageio. Changing to GIF format.")
                    save_path = save_path.replace('.mp4', '.gif')
            
            print(f"🎬 Creating diffusion animation...")
            print(f"  Steps to animate: {len(results['all_steps_grasps'])}")
            print(f"  Output resolution: {resolution}")
            print(f"  Frame rate: {fps} FPS")
            print(f"  Save path: {save_path}")
            
            # Prepare point cloud data
            pc_np = results["pc"].detach().cpu().numpy()
            final_confidence = results["confidence"].detach().cpu().numpy() if show_confidence_colors else None
            
            # Create frames for each diffusion step
            frames = []
            all_steps = results['all_steps_grasps']
            total_steps = len(all_steps)
            
            for step_idx, step_grasps in enumerate(all_steps):
                if show_progress:
                    print(f"  Rendering frame {step_idx + 1}/{total_steps}...", end='\r')
                
                # Convert to numpy
                step_grasps_np = step_grasps.detach().cpu().numpy()
                
                # Use confidence colors only for the final step
                confidence_colors = final_confidence if (step_idx == total_steps - 1 and show_confidence_colors) else None
                
                # Create scene for this step
                scene = visualize_pc_grasps(pc_np, step_grasps_np, confidence_colors)
                
                # Set up camera
                bounds = scene.bounds
                center = bounds.mean(axis=0)
                scale = np.max(bounds[1] - bounds[0])
                
                # Configure camera position for consistent viewpoint
                camera_transform = np.eye(4)
                camera_transform[:3, 3] = center + np.array([0, 0, scale * camera_distance])
                
                # Render frame
                try:
                    # Use offscreen rendering
                    png_data = scene.save_image(
                        resolution=resolution,
                        camera_transform=camera_transform,
                        scene_lights='default'
                    )
                    
                    # Convert to PIL Image
                    if isinstance(png_data, bytes):
                        from io import BytesIO
                        frame = Image.open(BytesIO(png_data))
                    else:
                        frame = Image.fromarray(png_data)
                    
                    frames.append(frame)
                    
                except Exception as e:
                    print(f"\n⚠️  Warning: Failed to render frame {step_idx + 1}: {e}")
                    print("    Falling back to basic rendering...")
                    
                    # Fallback: create a simple placeholder frame
                    placeholder = Image.new('RGB', resolution, color=(50, 50, 50))
                    frames.append(placeholder)
            
            if show_progress:
                print(f"  Rendered {len(frames)} frames successfully")
            
            # Save animation
            if len(frames) == 0:
                print("❌ No frames were rendered successfully")
                return False
            
            print(f"  💾 Saving animation...")
            
            # Calculate frame duration in milliseconds
            frame_duration = int(1000 / fps)
            
            if use_imageio and save_path.endswith('.mp4'):
                # Save as MP4 using imageio
                with imageio.get_writer(save_path, fps=fps, codec='libx264') as writer:
                    for frame in frames:
                        writer.append_data(np.array(frame))
            else:
                # Save as GIF using PIL or imageio
                if use_imageio:
                    imageio.mimsave(save_path, [np.array(frame) for frame in frames], 
                                  duration=frame_duration/1000.0, loop=0)
                else:
                    frames[0].save(
                        save_path,
                        save_all=True,
                        append_images=frames[1:],
                        duration=frame_duration,
                        loop=0,
                        optimize=True
                    )
            
            # Validate output file
            import os
            if os.path.exists(save_path):
                file_size = os.path.getsize(save_path)
                print(f"✅ Animation saved successfully!")
                print(f"  File: {save_path}")
                print(f"  Size: {file_size / (1024*1024):.2f} MB")
                print(f"  Frames: {len(frames)}")
                print(f"  Duration: {len(frames) / fps:.1f} seconds")
                return True
            else:
                print(f"❌ Failed to create animation file: {save_path}")
                return False
                
        except ImportError as e:
            print(f"❌ Missing required dependency for animation: {e}")
            print("Please install required packages: pip install trimesh pillow imageio imageio-ffmpeg")
            return False
        except Exception as e:
            print(f"❌ Failed to create animation: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _visualize_with_6dof_scene(
        self,
        results: Dict[str, torch.Tensor],
        save_path: Optional[str] = None,
        show_scene: bool = True,
        return_scene: bool = False,
        # Advanced visualization options
        visualize_diverse_grasps: bool = False,
        min_separation_distance: float = 0.03,
        plasma_coloring: bool = False,
        gripper_color: Tuple[float, float, float] = (0, 1, 0),
        show_gripper_mesh: bool = False
    ) -> Optional[Any]:
        """
        Enhanced visualization using 6DOF-GraspNet's draw_scene function.

        This method provides superior grasp visualization capabilities compared to the
        standard visualization, including:
        - Automatic grasp scoring color mapping (plasma colormap)
        - Grasp diversity filtering to reduce visual clutter
        - Advanced gripper rendering (line-based or full mesh)
        - Point cloud height-based coloring options
        - Intelligent grasp downsampling for performance

        Args:
            results: Results dictionary from inference containing 'pc', 'grasps', etc.
            save_path: Optional path to save the visualization
            show_scene: Whether to display the scene interactively
            return_scene: Whether to return the scene object
            visualize_diverse_grasps: If True, filter grasps for visual diversity
            min_separation_distance: Minimum distance between diverse grasps (meters)
            plasma_coloring: If True, color point cloud by height using plasma colormap
            gripper_color: Default RGB color for grippers (0-1 range)
            show_gripper_mesh: If True, show full gripper mesh; otherwise use lines

        Returns:
            trimesh.Scene object if return_scene=True, otherwise None

        Raises:
            ImportError: If 6DOF-GraspNet visualization components are not available
            ValueError: If required data is missing from results dictionary
        """
        # Verify 6DOF-GraspNet availability
        if not SIXDOF_AVAILABLE:
            raise ImportError("6DOF-GraspNet not available. Cannot use enhanced visualization.")
        
        # Validate required data in results
        required_keys = ["pc", "grasps"]
        missing_keys = [key for key in required_keys if key not in results]
        if missing_keys:
            raise ValueError(f"Missing required data in results: {missing_keys}")

        # Prepare data for visualization
        try:
            # Convert point cloud data
            pc_np = results["pc"].detach().cpu().numpy()
            
            # Convert grasp transformation matrices  
            grasps_np = results["grasps"].detach().cpu().numpy()
            
            # Select optimal scores for grasp coloring (prefer 6DOF scores)
            scores_np = None
            if "sixdof_scores" in results:
                scores_np = results["sixdof_scores"].detach().cpu().numpy()
                print(f"  Using 6DOF evaluation scores for grasp coloring")
            elif "refined_scores" in results:
                scores_np = results["refined_scores"].detach().cpu().numpy()
                print(f"  Using refined scores for grasp coloring")
            elif "confidence" in results:
                # Flatten confidence scores if needed
                confidence_tensor = results["confidence"]
                if confidence_tensor.dim() > 1:
                    scores_np = confidence_tensor.squeeze(-1).detach().cpu().numpy()
                else:
                    scores_np = confidence_tensor.detach().cpu().numpy()
                print(f"  Using graspLDM confidence scores for grasp coloring")
            else:
                print(f"  No scores available for grasp coloring")

            print(f"  📊 Visualization data prepared:")
            print(f"    Point cloud: {pc_np.shape} points")
            print(f"    Grasps: {grasps_np.shape[0]} poses")
            if scores_np is not None:
                print(f"    Scores: range [{scores_np.min():.3f}, {scores_np.max():.3f}], mean {scores_np.mean():.3f}")

        except Exception as e:
            raise ValueError(f"Failed to prepare visualization data: {e}")

        # Invoke enhanced 6DOF visualization
        try:
            scene = draw_scene(
                pc=pc_np,
                grasps=grasps_np,
                grasp_scores=scores_np,
                gripper_color=gripper_color,
                visualize_diverse_grasps=visualize_diverse_grasps,
                min_seperation_distance=min_separation_distance,
                plasma_coloring=plasma_coloring,
                show_gripper_mesh=show_gripper_mesh,
                save_path=save_path,
                show_scene=show_scene
            )
            
            # Provide feedback on visualization features used
            features_used = []
            if visualize_diverse_grasps:
                features_used.append(f"diversity filtering (min dist: {min_separation_distance:.3f}m)")
            if plasma_coloring:
                features_used.append("plasma height coloring")
            if show_gripper_mesh:
                features_used.append("full gripper meshes")
            if save_path:
                features_used.append(f"saved to {save_path}")
                
            if features_used:
                print(f"  ✨ Enhanced features: {', '.join(features_used)}")

            if return_scene:
                return scene
            else:
                return None

        except Exception as e:
            # Provide detailed error information for debugging
            print(f"❌ Enhanced visualization failed: {e}")
            print(f"   Point cloud shape: {pc_np.shape}")
            print(f"   Grasps shape: {grasps_np.shape}")
            if scores_np is not None:
                print(f"   Scores shape: {scores_np.shape}")
            raise RuntimeError(f"draw_scene visualization failed: {e}")

    def visualize_enhanced(
        self,
        results: Dict[str, torch.Tensor],
        save_path: Optional[str] = None,
        show_scene: bool = True,
        return_scene: bool = False,
        # Enhanced visualization options
        visualize_diverse_grasps: bool = True,
        min_separation_distance: float = 0.03,
        plasma_coloring: bool = False,
        gripper_color: Tuple[float, float, float] = (0, 1, 0),
        show_gripper_mesh: bool = False,
        # Fallback options
        fallback_to_standard: bool = True
    ) -> Optional[Any]:
        """
        Public interface for enhanced 6DOF-GraspNet visualization.

        This method provides an easy-to-use interface for the enhanced visualization
        capabilities, with automatic fallback to standard visualization if needed.

        Args:
            results: Results dictionary from inference containing 'pc', 'grasps', etc.
            save_path: Optional path to save the visualization
            show_scene: Whether to display the scene interactively
            return_scene: Whether to return the scene object
            visualize_diverse_grasps: If True, filter grasps for visual diversity
            min_separation_distance: Minimum distance between diverse grasps (meters)
            plasma_coloring: If True, color point cloud by height using plasma colormap
            gripper_color: Default RGB color for grippers (0-1 range)
            show_gripper_mesh: If True, show full gripper mesh; otherwise use lines
            fallback_to_standard: If True, use standard visualization if enhanced fails

        Returns:
            trimesh.Scene object if return_scene=True, otherwise None

        Example:
            >>> # Basic enhanced visualization
            >>> inference.visualize_enhanced(results, save_path="enhanced_grasps.glb")
            
            >>> # With diversity filtering and custom colors
            >>> scene = inference.visualize_enhanced(
            ...     results=results,
            ...     visualize_diverse_grasps=True,
            ...     min_separation_distance=0.05,
            ...     plasma_coloring=True,
            ...     gripper_color=(1, 0, 0),  # Red grippers
            ...     return_scene=True
            ... )
        """
        # Try enhanced visualization first
        if SIXDOF_AVAILABLE:
            try:
                return self._visualize_with_6dof_scene(
                    results=results,
                    save_path=save_path,
                    show_scene=show_scene,
                    return_scene=return_scene,
                    visualize_diverse_grasps=visualize_diverse_grasps,
                    min_separation_distance=min_separation_distance,
                    plasma_coloring=plasma_coloring,
                    gripper_color=gripper_color,
                    show_gripper_mesh=show_gripper_mesh
                )
            except Exception as e:
                print(f"⚠️  Enhanced visualization failed: {e}")
                if not fallback_to_standard:
                    raise RuntimeError(f"Enhanced visualization failed and fallback disabled: {e}")
                print(f"   Falling back to standard visualization...")

        # Fallback to standard visualization
        if fallback_to_standard:
            print(f"  Using standard visualization...")
            return self._visualize_results(results, return_scene, save_path)
        else:
            raise RuntimeError("Enhanced visualization not available and fallback disabled")

    def visualize_refinement_process(
        self,
        refinement_data: Dict[str, Any],
        save_path: Optional[str] = None,
        show_scene: bool = True,
        return_scene: bool = False,
        # Refinement visualization options
        highlight_selected_grasps: bool = True,
        color_by_score_improvement: bool = True,
        step_transparency_fade: bool = True,
        # Enhanced visualization options  
        visualize_diverse_grasps: bool = True,
        min_separation_distance: float = 0.03,
        gripper_color: Tuple[float, float, float] = (0, 1, 0),
        show_gripper_mesh: bool = False
    ) -> Optional[Any]:
        """
        Visualize the complete grasp refinement optimization process.

        This method provides comprehensive visualization of how grasps evolve during the
        6DOF-GraspNet refinement process, showing:
        - Grasp poses at each refinement step
        - Success probability improvements over time  
        - Selected vs. discarded grasps
        - Spatial trajectory of pose optimization
        
        Args:
            refinement_data: Tracking data from _refine_grasps_with_6dof with track_refinement_process=True
            save_path: Optional path to save the visualization
            show_scene: Whether to display the scene interactively
            return_scene: Whether to return the scene object
            highlight_selected_grasps: If True, emphasize grasps selected by strategy
            color_by_score_improvement: If True, color grasps by score improvement
            step_transparency_fade: If True, make earlier steps more transparent
            visualize_diverse_grasps: If True, filter grasps for visual diversity
            min_separation_distance: Minimum distance between diverse grasps (meters)
            gripper_color: Base RGB color for grippers (0-1 range)
            show_gripper_mesh: If True, show full gripper mesh; otherwise use lines

        Returns:
            trimesh.Scene object if return_scene=True, otherwise None
            
        Raises:
            ValueError: If refinement_data is None or missing required keys
            ImportError: If 6DOF-GraspNet visualization components are not available

        Example:
            >>> # First run inference with refinement tracking
            >>> refined_grasps, refined_scores, refinement_data = inference._refine_grasps_with_6dof(
            ...     pc, grasps, track_refinement_process=True
            ... )
            >>> 
            >>> # Then visualize the optimization process
            >>> scene = inference.visualize_refinement_process(
            ...     refinement_data=refinement_data,
            ...     save_path="refinement_process.glb",
            ...     color_by_score_improvement=True,
            ...     step_transparency_fade=True,
            ...     return_scene=True
            ... )
        """
        # Verify 6DOF-GraspNet availability
        if not SIXDOF_AVAILABLE:
            raise ImportError("6DOF-GraspNet not available. Cannot visualize refinement process.")
            
        # Validate refinement data
        if refinement_data is None:
            raise ValueError("refinement_data is None. Set track_refinement_process=True in _refine_grasps_with_6dof")
            
        required_keys = ['improved_eulers', 'improved_ts', 'improved_success', 'pointcloud_original']
        missing_keys = [key for key in required_keys if key not in refinement_data]
        if missing_keys:
            raise ValueError(f"Missing required keys in refinement_data: {missing_keys}")

        try:
            # Extract data from refinement tracking
            improved_eulers = refinement_data['improved_eulers']      # [S, G, 3]
            improved_ts = refinement_data['improved_ts']              # [S, G, 3] 
            improved_success = refinement_data['improved_success']    # [S, G]
            selection_mask = refinement_data['selection_mask']        # [S, G]
            pc_original = refinement_data['pointcloud_original']      # [N, 3]
            pc_mean = refinement_data['pc_mean']                      # [3]
            
            num_steps, num_grasps, _ = improved_eulers.shape
            
            print(f"  🔄 Visualizing refinement process:")
            print(f"    Steps: {num_steps} (initial → {num_steps-2} refinements → final)")
            print(f"    Grasps per step: {num_grasps}")
            print(f"    Total poses: {num_steps * num_grasps}")

            # Prepare all grasp poses for visualization
            all_grasps_list = []
            all_scores_list = []
            all_colors_list = []
            
            # Color schemes for different visualization modes
            if color_by_score_improvement:
                # Calculate score improvements from initial to final
                initial_scores = improved_success[0]  # [G] initial scores
                final_scores = improved_success[-1]   # [G] final scores  
                score_improvements = final_scores - initial_scores  # [G]
                
                # Normalize improvements to [0, 1] for color mapping
                if score_improvements.max() > score_improvements.min():
                    norm_improvements = (score_improvements - score_improvements.min()) / \
                                      (score_improvements.max() - score_improvements.min())
                else:
                    norm_improvements = np.ones_like(score_improvements) * 0.5
                    
                print(f"    Score improvements: range [{score_improvements.min():.3f}, {score_improvements.max():.3f}]")

            # Process each refinement step
            for step_idx in range(num_steps):
                step_eulers = improved_eulers[step_idx]  # [G, 3]
                step_ts = improved_ts[step_idx]          # [G, 3]
                step_scores = improved_success[step_idx] # [G]
                
                # Convert euler+translation to homogeneous matrices (centered coordinates)
                step_grasps_centered = grasp_utils.rot_and_trans_to_grasps(
                    step_eulers[np.newaxis], step_ts[np.newaxis], 
                    np.ones((1, num_grasps))  # Select all grasps for this step
                )
                step_grasps_centered = np.stack(step_grasps_centered)  # [G, 4, 4]
                
                # Transform back to original coordinates
                step_grasps_original = step_grasps_centered.copy()
                step_grasps_original[..., :3, 3] += pc_mean  # Add back centroid offset
                
                # Determine colors and transparency for this step
                if color_by_score_improvement:
                    # Use plasma colormap for score improvements
                    import matplotlib.pyplot as plt
                    colormap = plt.cm.plasma
                    step_colors = colormap(norm_improvements)[:, :3]  # [G, 3] RGB
                else:
                    # Use step-based coloring (coolwarm from blue to red)
                    import matplotlib.pyplot as plt
                    coolwarm = plt.cm.coolwarm
                    step_progress = step_idx / (num_steps - 1) if num_steps > 1 else 0
                    step_color = coolwarm(step_progress)[:3]  # RGB
                    step_colors = np.tile(step_color, (num_grasps, 1))  # [G, 3]
                
                # Apply transparency fade if enabled
                if step_transparency_fade:
                    # Earlier steps more transparent, final step fully opaque
                    alpha = 0.3 + 0.7 * (step_idx / (num_steps - 1)) if num_steps > 1 else 1.0
                    step_colors = np.column_stack([step_colors, np.full(num_grasps, alpha)])  # [G, 4] RGBA
                else:
                    step_colors = np.column_stack([step_colors, np.ones(num_grasps)])  # [G, 4] RGBA

                # Apply selection highlighting if enabled
                if highlight_selected_grasps:
                    selected_in_step = selection_mask[step_idx]  # [G] binary mask
                    # Dim non-selected grasps
                    non_selected_mask = selected_in_step == 0
                    step_colors[non_selected_mask, 3] *= 0.3  # Reduce alpha for non-selected
                    
                all_grasps_list.extend(step_grasps_original)
                all_scores_list.extend(step_scores)
                all_colors_list.extend(step_colors)

            # Convert to numpy arrays
            all_grasps_np = np.array(all_grasps_list)  # [S*G, 4, 4]
            all_scores_np = np.array(all_scores_list)  # [S*G]
            all_colors_np = np.array(all_colors_list)  # [S*G, 4]
            
            print(f"    Prepared {len(all_grasps_np)} total poses for visualization")

            # Apply diversity filtering if requested
            if visualize_diverse_grasps and len(all_grasps_np) > 50:  # Only for large datasets
                # Simple diversity filtering based on translation distance
                translations = all_grasps_np[:, :3, 3]  # [S*G, 3]
                diverse_indices = []
                
                for i, trans in enumerate(translations):
                    if not diverse_indices:
                        diverse_indices.append(i)
                        continue
                        
                    # Check distance to all previously selected poses
                    selected_trans = translations[diverse_indices]
                    distances = np.linalg.norm(selected_trans - trans, axis=1)
                    
                    if np.min(distances) >= min_separation_distance:
                        diverse_indices.append(i)
                
                # Apply filtering
                all_grasps_np = all_grasps_np[diverse_indices]
                all_scores_np = all_scores_np[diverse_indices] 
                all_colors_np = all_colors_np[diverse_indices]
                
                print(f"    Applied diversity filtering: {len(all_grasps_np)} poses remaining")

            # Use 6DOF-GraspNet's draw_scene for visualization
            scene = draw_scene(
                pc=pc_original,
                grasps=all_grasps_np,
                grasp_scores=all_scores_np,
                gripper_color=gripper_color,
                visualize_diverse_grasps=False,  # Already applied manually
                min_seperation_distance=min_separation_distance,
                plasma_coloring=False,  # Using custom colors
                show_gripper_mesh=show_gripper_mesh,
                save_path=save_path,
                show_scene=show_scene
            )
            
            # Add step labels and metadata to scene if possible
            if hasattr(scene, 'metadata'):
                scene.metadata['refinement_steps'] = num_steps
                scene.metadata['total_grasps'] = num_steps * num_grasps
                scene.metadata['selected_grasps'] = np.sum(selection_mask)
                scene.metadata['refinement_method'] = refinement_data.get('method', 'unknown')
                
            print(f"  ✅ Refinement process visualization complete")
            
            if return_scene:
                return scene
            else:
                return None

        except Exception as e:
            raise RuntimeError(f"Refinement process visualization failed: {e}")

    def plot_refinement_metrics(
        self,
        refinement_data: Dict[str, Any],
        save_path: Optional[str] = None,
        show_plot: bool = True,
        # Plot configuration
        figsize: Tuple[int, int] = (12, 8),
        show_individual_grasps: bool = True,
        show_statistics: bool = True,
        show_selection_strategy: bool = True
    ) -> bool:
        """
        Create detailed plots showing grasp refinement metrics and score evolution.

        This method generates comprehensive analytical plots of the refinement process:
        - Score evolution over refinement steps
        - Individual grasp trajectories  
        - Statistical summaries (mean, std, min, max)
        - Selection strategy visualization
        - Score improvement distributions

        Args:
            refinement_data: Tracking data from _refine_grasps_with_6dof with track_refinement_process=True
            save_path: Optional path to save the plot (supports .png, .pdf, .svg)
            show_plot: Whether to display the plot interactively
            figsize: Figure size (width, height) in inches
            show_individual_grasps: If True, show individual grasp score trajectories
            show_statistics: If True, show statistical summaries
            show_selection_strategy: If True, highlight selected grasps

        Returns:
            bool: True if plot was created successfully, False otherwise

        Example:
            >>> # Create comprehensive refinement analysis plots
            >>> success = inference.plot_refinement_metrics(
            ...     refinement_data=refinement_data,
            ...     save_path="refinement_analysis.png",
            ...     show_individual_grasps=True,
            ...     show_statistics=True
            ... )
        """
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as mpatches
            
            # Validate refinement data
            if refinement_data is None:
                print("❌ refinement_data is None. Set track_refinement_process=True in _refine_grasps_with_6dof")
                return False
                
            # Extract data
            improved_success = refinement_data['improved_success']    # [S, G]
            selection_mask = refinement_data['selection_mask']        # [S, G]
            method = refinement_data.get('method', 'unknown')
            threshold = refinement_data.get('threshold', 0.0)
            num_steps, num_grasps = improved_success.shape
            
            step_labels = ['Initial'] + [f'Step {i+1}' for i in range(num_steps-2)] + ['Final']
            
            print(f"  📊 Creating refinement metrics plots:")
            print(f"    Method: {method}")
            print(f"    Steps: {num_steps}")
            print(f"    Grasps: {num_grasps}")

            # Create subplot layout
            fig = plt.figure(figsize=figsize)
            
            # Main score evolution plot
            ax1 = plt.subplot(2, 3, (1, 2))  # Spans two columns
            
            # Individual grasp trajectories
            if show_individual_grasps:
                for g in range(num_grasps):
                    grasp_scores = improved_success[:, g]
                    
                    # Color based on selection
                    if show_selection_strategy:
                        # Check if this grasp was ever selected
                        grasp_selected = np.any(selection_mask[:, g])
                        color = 'blue' if grasp_selected else 'lightgray'
                        alpha = 0.8 if grasp_selected else 0.3
                        linewidth = 1.5 if grasp_selected else 0.5
                    else:
                        color = 'blue'
                        alpha = 0.6
                        linewidth = 1.0
                    
                    ax1.plot(range(num_steps), grasp_scores, 
                           color=color, alpha=alpha, linewidth=linewidth)
            
            # Statistical summaries
            if show_statistics:
                means = np.mean(improved_success, axis=1)
                stds = np.std(improved_success, axis=1)
                mins = np.min(improved_success, axis=1)
                maxs = np.max(improved_success, axis=1)
                
                ax1.plot(range(num_steps), means, 'r-', linewidth=3, label='Mean')
                ax1.fill_between(range(num_steps), means - stds, means + stds, 
                               color='red', alpha=0.2, label='±1 Std')
                ax1.plot(range(num_steps), mins, 'g--', linewidth=2, label='Min')
                ax1.plot(range(num_steps), maxs, 'purple', linestyle='--', linewidth=2, label='Max')
            
            # Add threshold line if applicable
            if threshold > 0:
                ax1.axhline(y=threshold, color='orange', linestyle=':', linewidth=2, 
                          label=f'Threshold ({threshold:.2f})')
            
            ax1.set_xlabel('Refinement Step')
            ax1.set_ylabel('Success Probability')
            ax1.set_title(f'Grasp Success Score Evolution ({method.title()} Method)')
            ax1.set_xticks(range(num_steps))
            ax1.set_xticklabels(step_labels, rotation=45, ha='right')
            ax1.grid(True, alpha=0.3)
            ax1.legend()
            ax1.set_ylim(0, 1)
            
            # Score improvement distribution
            ax2 = plt.subplot(2, 3, 3)
            initial_scores = improved_success[0]  # [G]
            final_scores = improved_success[-1]   # [G] 
            improvements = final_scores - initial_scores
            
            ax2.hist(improvements, bins=20, alpha=0.7, color='green', edgecolor='black')
            ax2.axvline(x=0, color='red', linestyle='--', label='No Change')
            ax2.axvline(x=np.mean(improvements), color='blue', linestyle='-', 
                       label=f'Mean ({np.mean(improvements):.3f})')
            ax2.set_xlabel('Score Improvement')
            ax2.set_ylabel('Number of Grasps')
            ax2.set_title('Score Improvement Distribution')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # Selection strategy visualization
            ax3 = plt.subplot(2, 3, 4)
            selected_counts = np.sum(selection_mask, axis=1)
            selection_percentages = selected_counts / num_grasps * 100
            
            bars = ax3.bar(range(num_steps), selection_percentages, 
                          color='skyblue', edgecolor='navy', alpha=0.7)
            ax3.set_xlabel('Refinement Step')
            ax3.set_ylabel('Selection Percentage (%)')
            ax3.set_title('Grasp Selection by Step')
            ax3.set_xticks(range(num_steps))
            ax3.set_xticklabels(step_labels, rotation=45, ha='right')
            ax3.grid(True, alpha=0.3, axis='y')
            
            # Add count labels on bars
            for i, (bar, count) in enumerate(zip(bars, selected_counts)):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{count}', ha='center', va='bottom', fontsize=9)
            
            # Score statistics table
            ax4 = plt.subplot(2, 3, 5)
            ax4.axis('off')
            
            # Create statistics table
            stats_data = []
            for step_idx in range(num_steps):
                step_scores = improved_success[step_idx]
                stats_data.append([
                    step_labels[step_idx],
                    f'{np.mean(step_scores):.3f}',
                    f'{np.std(step_scores):.3f}',
                    f'{np.min(step_scores):.3f}',
                    f'{np.max(step_scores):.3f}',
                    f'{np.sum(selection_mask[step_idx])}'
                ])
            
            table = ax4.table(cellText=stats_data,
                            colLabels=['Step', 'Mean', 'Std', 'Min', 'Max', 'Selected'],
                            cellLoc='center',
                            loc='center')
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 1.5)
            ax4.set_title('Score Statistics by Step')
            
            # Overall improvement summary
            ax5 = plt.subplot(2, 3, 6)
            
            # Create pie chart of improvement categories
            improved_grasps = np.sum(improvements > 0.01)  # Significantly improved
            degraded_grasps = np.sum(improvements < -0.01)  # Significantly degraded
            stable_grasps = num_grasps - improved_grasps - degraded_grasps
            
            labels = ['Improved', 'Stable', 'Degraded']
            sizes = [improved_grasps, stable_grasps, degraded_grasps]
            colors = ['green', 'yellow', 'red']
            explode = (0.1, 0, 0.1)  # Explode improved and degraded slices
            
            wedges, texts, autotexts = ax5.pie(sizes, labels=labels, colors=colors, 
                                             explode=explode, autopct='%1.1f%%',
                                             shadow=True, startangle=90)
            ax5.set_title('Overall Refinement Outcome')
            
            # Adjust layout
            plt.tight_layout()
            
            # Add overall figure title
            fig.suptitle(f'Grasp Refinement Analysis: {method.title()} Method', 
                        fontsize=16, fontweight='bold', y=0.98)
            
            # Save plot if requested
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"  ✅ Plot saved: {save_path}")
            
            # Show plot if requested
            if show_plot:
                plt.show()
            else:
                plt.close()
            
            print(f"  ✅ Refinement metrics plot created successfully")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create refinement metrics plot: {e}")
            import traceback
            traceback.print_exc()
            return False


def demo_simple_inference():
    """
    Demonstration of the simplified inference interface.

    This function shows how to use the SimpleGraspLDMInference class
    with synthetic data to generate grasps.
    """
    print("🎯 GraspLDM Simplified Inference Demo")
    print("=" * 50)

    # Initialize inference engine
    exp_path = "checkpoints/generation/ppc_1a_partial_63cat8k_filtered_latentc3_z16_pc256_180k"

    if not os.path.exists(exp_path):
        print(f"❌ Experiment path not found: {exp_path}")
        print("Please ensure you have downloaded the model checkpoints.")
        return

    try:
        inference = SimpleGraspLDMInference(
            exp_path=exp_path,
            device="cuda:0" if torch.cuda.is_available() else "cpu",
            num_inference_steps=100  # Faster for demo
        )

        # Generate synthetic point cloud (sphere)
        print("\n📊 Generating synthetic point cloud...")
        theta = np.random.uniform(0, 2*np.pi, 2000)
        phi = np.random.uniform(0, np.pi, 2000)
        r = 0.18  # 18cm radius sphere

        x = r * np.sin(phi) * np.cos(theta)
        y = r * np.sin(phi) * np.sin(theta)
        z = r * np.cos(phi)

        synthetic_pc = np.column_stack([x, y, z])
        print(f"✓ Created sphere point cloud: {synthetic_pc.shape}")

        # Generate grasps
        print("\n🤖 Generating grasps...")
        results = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=20,
            visualize=False  # Set to True to see 3D visualization
        )

        # Demonstrate intermediate steps feature
        print("\n🔍 Generating grasps with intermediate steps...")
        results_with_steps = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=10,
            return_intermediate=True,
            visualize=False
        )

        # Demonstrate enhanced 6DOF visualization
        print("\n🎨 Testing enhanced 6DOF visualization...")
        try:
            # Test the new public interface for enhanced visualization
            enhanced_viz_scene = inference.visualize_enhanced(
                results=results,
                save_path="demo_enhanced_grasps.glb",
                show_scene=False,  # Don't show interactively in demo
                return_scene=True,
                visualize_diverse_grasps=True,
                min_separation_distance=0.05,  # 5cm separation for demo
                plasma_coloring=True,  # Try plasma coloring
                show_gripper_mesh=False
            )
            print("  ✅ Enhanced visualization public interface works!")
            
            # Test integrated visualization in inference pipeline
            results_with_enhanced_viz = inference.infer_from_pointcloud(
                pointcloud=synthetic_pc,
                num_grasps=15,
                visualize=True,
                save_path="demo_integrated_enhanced.glb"
            )
            print("  ✅ Integrated enhanced visualization works!")
            
        except Exception as e:
            print(f"  ❌ Enhanced visualization failed: {e}")
            print("     This is expected if 6DOF-GraspNet is not available")

        # Demonstrate saving standard visualization
        print("\n💾 Saving standard visualization...")
        results_with_viz = inference.infer_from_pointcloud(
            pointcloud=synthetic_pc,
            num_grasps=20,
            visualize=True,
            save_path="demo_standard_grasps.glb"  # Save as GLB format
        )

        # Analyze results
        print(f"\n📈 Results Analysis:")
        print(f"  Generated grasps: {results['grasps'].shape[0]}")
        print(f"  Confidence range: {results['confidence'].min():.3f} - {results['confidence'].max():.3f}")
        print(f"  Mean confidence: {results['confidence'].mean():.3f}")
        
        # Analyze intermediate steps
        if results_with_steps.get('all_steps_grasps'):
            print(f"  Intermediate steps captured: {len(results_with_steps['all_steps_grasps'])}")
            print(f"  Each step shape: {results_with_steps['all_steps_grasps'][0].shape}")
            
            # Create diffusion animation
            print("\n🎬 Creating diffusion animation...")
            animation_success = inference.create_diffusion_animation(
                results=results_with_steps,
                save_path="demo_diffusion_evolution.gif",
                resolution=(800, 600),
                fps=3,
                show_confidence_colors=True
            )
            
            if animation_success:
                print("  ✅ Animation created successfully!")
            else:
                print("  ❌ Animation creation failed")
        else:
            print(f"  No intermediate steps captured (may require return_intermediate=True)")

        print(f"\n✅ Demo completed successfully!")

        
        # Demonstrate enhanced selection strategies (if 6DOF available)
        if inference.enable_grasp_refinement and SIXDOF_AVAILABLE:
            print(f"\n🎯 Demonstrating enhanced grasp selection strategies...")
            
            # Test different selection strategies
            strategies = ["all", "better_than_threshold", "better_than_threshold_in_sequence"]
            
            for strategy in strategies:
                if inference.choose_fns[strategy] is not None or strategy == "all":
                    print(f"\n  Testing '{strategy}' selection strategy...")
                    try:
                        strategy_results = inference.infer_from_pointcloud(
                            pointcloud=synthetic_pc,
                            num_grasps=10,
                            refine_grasps=True,
                            refinement_steps=3,  # Faster for demo
                            choose_fn=strategy,
                            visualize=False
                        )
                        
                        if 'refined_grasps' in strategy_results:
                            final_count = strategy_results['refined_grasps'].shape[0]
                            print(f"    ✅ Strategy '{strategy}': {final_count} final grasps")
                            if 'refined_scores' in strategy_results:
                                mean_score = strategy_results['refined_scores'].mean().item()
                                print(f"       Mean refined score: {mean_score:.3f}")
                        else:
                            print(f"    ⚠️  Strategy '{strategy}': Refinement failed")
                            
                    except Exception as e:
                        print(f"    ❌ Strategy '{strategy}' failed: {e}")
                else:
                    print(f"\n  Strategy '{strategy}': Not available (6DOF utils required)")

    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    demo_simple_inference()