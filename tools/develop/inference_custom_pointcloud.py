#!/usr/bin/env python3
"""
Custom Pointcloud Inference for graspLDM

This script demonstrates how to run graspLDM inference on your own pointcloud data.
It provides a complete pipeline from loading your custom pointcloud to generating
and visualizing grasp poses.

Usage:
    python tools/inference_custom_pointcloud.py \
        --pointcloud_path /path/to/your/pointcloud.npy \
        --exp_name YOUR_EXPERIMENT_NAME \
        --mode LDM \
        --num_grasps 20 \
        --visualize

Author: Generated for graspLDM
"""

import argparse
import os
import sys
from pathlib import Path

import numpy as np
import torch
import trimesh
from tools.inference import InferenceLDM, InferenceVAE, Conditioning


class CustomPointCloudInference:
    """
    A wrapper class to perform graspLDM inference on custom pointcloud data
    """
    
    def __init__(self, exp_name, mode="LDM", exp_out_root="output", 
                 use_ema_model=True, device="cuda:0"):
        """
        Initialize the custom pointcloud inference wrapper
        
        Args:
            exp_name (str): Experiment name (e.g., "acronym_fpc_ddpm")
            mode (str): Model mode, either "LDM" or "VAE"
            exp_out_root (str): Output root directory for experiments
            use_ema_model (bool): Whether to use EMA model weights
            device (str): Device to run inference on
        """
        self.mode = mode
        self.device = device
        
        # Initialize appropriate inference class
        if mode == "LDM":
            self.inference = InferenceLDM(
                exp_name=exp_name,
                exp_out_root=exp_out_root,
                use_ema_model=use_ema_model,
                load_dataset=False,  # Don't load dataset, we'll use custom data
                device=device
            )
        elif mode == "VAE":
            self.inference = InferenceVAE(
                exp_name=exp_name,
                exp_out_root=exp_out_root,
                use_ema_model=use_ema_model,
                load_dataset=False,  # Don't load dataset, we'll use custom data
                device=device
            )
        else:
            raise ValueError(f"Unsupported mode: {mode}. Use 'LDM' or 'VAE'")
    
    def load_pointcloud(self, pointcloud_path):
        """
        Load pointcloud from various file formats
        
        Args:
            pointcloud_path (str): Path to pointcloud file
            
        Returns:
            torch.Tensor: Pointcloud tensor of shape [N, 3]
        """
        pointcloud_path = Path(pointcloud_path)
        
        if not pointcloud_path.exists():
            raise FileNotFoundError(f"Pointcloud file not found: {pointcloud_path}")
        
        # Support multiple file formats
        if pointcloud_path.suffix == '.npy':
            pc = np.load(pointcloud_path)
        elif pointcloud_path.suffix == '.npz':
            data = np.load(pointcloud_path)
            # Assume pointcloud is stored under 'points' key, modify as needed
            pc = data['points'] if 'points' in data else data[list(data.keys())[0]]
        elif pointcloud_path.suffix == '.txt':
            pc = np.loadtxt(pointcloud_path)
        elif pointcloud_path.suffix in ['.ply', '.obj', '.stl']:
            # Load mesh and sample points
            mesh = trimesh.load(pointcloud_path)
            pc, _ = trimesh.sample.sample_surface(mesh, 2048)
        else:
            raise ValueError(f"Unsupported file format: {pointcloud_path.suffix}")
        
        # Ensure pointcloud is [N, 3]
        pc = np.array(pc)
        if pc.shape[1] != 3:
            if pc.shape[0] == 3:
                pc = pc.T  # Transpose if it's [3, N]
            else:
                raise ValueError(f"Expected pointcloud shape [N, 3], got {pc.shape}")
        
        return torch.from_numpy(pc).float()
    
    def preprocess_pointcloud(self, pc, target_num_points=1024):
        """
        Preprocess pointcloud to match graspLDM's expected input format
        
        Args:
            pc (torch.Tensor): Raw pointcloud [N, 3]
            target_num_points (int): Target number of points to sample
            
        Returns:
            tuple: (processed_pc, original_stats) where processed_pc is ready for inference
                   and original_stats contains information for potential post-processing
        """
        original_stats = {
            'original_shape': pc.shape,
            'original_mean': pc.mean(dim=0),
            'original_std': pc.std(dim=0),
            'original_bounds': (pc.min(dim=0)[0], pc.max(dim=0)[0])
        }
        
        print(f"Original pointcloud shape: {pc.shape}")
        print(f"Original pointcloud bounds: min={original_stats['original_bounds'][0]}, max={original_stats['original_bounds'][1]}")
        
        # 1. Handle point count
        if pc.shape[0] < target_num_points:
            print(f"Upsampling pointcloud from {pc.shape[0]} to {target_num_points} points")
            # Upsample by repeating points with small noise
            indices = torch.randint(0, pc.shape[0], (target_num_points,))
            pc = pc[indices]
            # Add small noise to avoid identical points
            noise = torch.randn_like(pc) * 0.001
            pc = pc + noise
        elif pc.shape[0] > target_num_points:
            print(f"Downsampling pointcloud from {pc.shape[0]} to {target_num_points} points")
            # Randomly subsample
            indices = torch.randperm(pc.shape[0])[:target_num_points]
            pc = pc[indices]
        
        # 2. Remove outliers (optional but recommended)
        pc = self._remove_outliers(pc)
        
        # 3. Ensure proper data type and device
        pc = pc.float().to(self.device)
        
        print(f"Preprocessed pointcloud shape: {pc.shape}")
        
        return pc, original_stats
    
    def _remove_outliers(self, pc, std_factor=2.0):
        """
        Remove outlier points that are too far from the point cloud center
        
        Args:
            pc (torch.Tensor): Pointcloud [N, 3]
            std_factor (float): Standard deviation factor for outlier detection
            
        Returns:
            torch.Tensor: Filtered pointcloud
        """
        center = pc.mean(dim=0)
        distances = torch.norm(pc - center, dim=1)
        threshold = distances.mean() + std_factor * distances.std()
        
        mask = distances <= threshold
        filtered_pc = pc[mask]
        
        if filtered_pc.shape[0] < pc.shape[0] * 0.8:  # Don't remove more than 20%
            print(f"Warning: Outlier removal would remove too many points ({pc.shape[0] - filtered_pc.shape[0]})")
            return pc
        
        if filtered_pc.shape[0] != pc.shape[0]:
            print(f"Removed {pc.shape[0] - filtered_pc.shape[0]} outlier points")
        
        return filtered_pc
    
    def infer_grasps(self, pc, num_grasps=10, condition_type=Conditioning.UNCONDITIONAL, 
                     class_label=0, region_id=0, return_intermediate=False):
        """
        Run grasp inference on the preprocessed pointcloud
        
        Args:
            pc (torch.Tensor): Preprocessed pointcloud [N, 3]
            num_grasps (int): Number of grasps to generate
            condition_type (Conditioning): Type of conditioning to use
            class_label (int): Class label for class conditioning (0-based)
            region_id (int): Region ID for region conditioning
            return_intermediate (bool): Whether to return intermediate diffusion steps (LDM only)
            
        Returns:
            dict: Inference results containing grasps, confidence scores, etc.
        """
        print(f"Running {self.mode} inference...")
        print(f"  - Num grasps: {num_grasps}")
        print(f"  - Condition type: {condition_type.value}")
        
        if condition_type == Conditioning.UNCONDITIONAL:
            results = self.inference.infer_on_pointcloud(
                pc, 
                num_grasps=num_grasps,
                return_intermediate=return_intermediate
            )
        elif condition_type == Conditioning.CLASS_CONDITIONED and self.mode == "LDM":
            print(f"  - Class label: {class_label}")
            results = self.inference.generate_class_conditioned_grasps(
                pc, 
                num_grasps=num_grasps,
                class_label=class_label
            )
        elif condition_type == Conditioning.REGION_CONDITIONED and self.mode == "LDM":
            print(f"  - Region ID: {region_id}")
            # Note: For region conditioning, you'll need to provide region_points
            # This is a simplified example - in practice, you'd extract regions from your pointcloud
            print("Warning: Region conditioning requires manual region specification")
            results = self.inference.infer_on_pointcloud(pc, num_grasps=num_grasps)
        else:
            if self.mode == "VAE":
                print("Warning: VAE mode only supports unconditional generation, falling back to unconditional")
            results = self.inference.infer_on_pointcloud(pc, num_grasps=num_grasps)
        
        # Extract key results
        grasps = results['grasps'][0]  # [num_grasps, 4, 4] homogeneous transform matrices
        confidence = results['confidence'][0]  # [num_grasps, 1] confidence scores
        pointcloud = results['pc'][0]  # [N, 3] unnormalized pointcloud
        
        print(f"Generated {grasps.shape[0]} grasps with confidence scores")
        print(f"Confidence range: {confidence.min().item():.3f} - {confidence.max().item():.3f}")
        
        return {
            'grasps': grasps,
            'confidence': confidence,
            'pointcloud': pointcloud,
            'raw_results': results
        }
    
    def filter_grasps_by_confidence(self, results, min_confidence=0.5):
        """
        Filter grasps by confidence threshold
        
        Args:
            results (dict): Results from infer_grasps
            min_confidence (float): Minimum confidence threshold
            
        Returns:
            dict: Filtered results
        """
        confidence = results['confidence']
        mask = confidence.squeeze() >= min_confidence
        
        if mask.sum() == 0:
            print(f"Warning: No grasps above confidence threshold {min_confidence}")
            return results
        
        filtered_results = {
            'grasps': results['grasps'][mask],
            'confidence': results['confidence'][mask],
            'pointcloud': results['pointcloud'],
            'raw_results': results['raw_results']
        }
        
        print(f"Filtered to {filtered_results['grasps'].shape[0]} grasps above confidence {min_confidence}")
        
        return filtered_results
    
    def visualize_results(self, results, show_axes=True, window_size=(1280, 960)):
        """
        Visualize the inference results
        
        Args:
            results (dict): Results from infer_grasps
            show_axes (bool): Whether to show coordinate axes
            window_size (tuple): Window size for visualization
        """
        print("Visualizing results...")
        
        try:
            # Use graspLDM's built-in visualization
            scene = self.inference.visualize(
                results['pointcloud'],
                results['grasps'],
                results['confidence'],
                return_scene=True
            )
            
            if show_axes:
                # Add coordinate axes for reference
                axes = trimesh.creation.axis(axis_length=0.1)
                scene.add_geometry(axes)
            
            scene.show()
            
        except Exception as e:
            print(f"Visualization failed: {e}")
            print("Falling back to basic mesh export...")
            self._save_results_as_mesh(results, "grasp_results.ply")
    
    def _save_results_as_mesh(self, results, output_path):
        """
        Save results as mesh files for external visualization
        
        Args:
            results (dict): Results from infer_grasps
            output_path (str): Output file path
        """
        # Convert pointcloud to mesh
        pc_mesh = trimesh.PointCloud(results['pointcloud'].cpu().numpy())
        
        # Create grasp visualization meshes
        grasp_meshes = []
        for i, (grasp, conf) in enumerate(zip(results['grasps'], results['confidence'])):
            # Create a simple gripper representation
            grasp_matrix = grasp.cpu().numpy()
            
            # Create a small coordinate frame at each grasp pose
            frame = trimesh.creation.axis(axis_length=0.02, transform=grasp_matrix)
            grasp_meshes.append(frame)
        
        # Combine all meshes
        combined = trimesh.Scene([pc_mesh] + grasp_meshes)
        combined.export(output_path)
        print(f"Results saved to {output_path}")


def main():
    parser = argparse.ArgumentParser(description="Run graspLDM inference on custom pointcloud")
    
    # Required arguments
    parser.add_argument("--pointcloud_path", type=str, required=True,
                       help="Path to your pointcloud file (.npy, .npz, .txt, .ply, .obj, .stl)")
    parser.add_argument("--exp_name", type=str, required=True,
                       help="Experiment name (e.g., 'acronym_fpc_ddpm')")
    
    # Model arguments
    parser.add_argument("--mode", type=str, choices=["LDM", "VAE"], default="LDM",
                       help="Model mode to use")
    parser.add_argument("--exp_out_root", type=str, default="output",
                       help="Experiment output root directory")
    parser.add_argument("--use_ema_model", action="store_true", default=True,
                       help="Use EMA model weights")
    parser.add_argument("--device", type=str, default="cuda:0",
                       help="Device to run inference on")
    
    # Inference arguments
    parser.add_argument("--num_grasps", type=int, default=10,
                       help="Number of grasps to generate")
    parser.add_argument("--target_points", type=int, default=1024,
                       help="Target number of points for preprocessing")
    parser.add_argument("--min_confidence", type=float, default=0.3,
                       help="Minimum confidence threshold for filtering grasps")
    
    # Conditioning arguments
    parser.add_argument("--condition_type", type=str, 
                       choices=["UNCONDITIONAL", "CLASS_CONDITIONED", "REGION_CONDITIONED"], 
                       default="UNCONDITIONAL",
                       help="Type of conditioning to use")
    parser.add_argument("--class_label", type=int, default=0,
                       help="Class label for class conditioning")
    parser.add_argument("--region_id", type=int, default=0,
                       help="Region ID for region conditioning")
    
    # Output arguments
    parser.add_argument("--visualize", action="store_true",
                       help="Visualize results")
    parser.add_argument("--save_results", type=str, default=None,
                       help="Save results to file (.ply)")
    
    args = parser.parse_args()
    
    # Convert condition type string to enum
    condition_type = getattr(Conditioning, args.condition_type)
    
    try:
        # Initialize inference
        print(f"Initializing {args.mode} inference for experiment: {args.exp_name}")
        inference_wrapper = CustomPointCloudInference(
            exp_name=args.exp_name,
            mode=args.mode,
            exp_out_root=args.exp_out_root,
            use_ema_model=args.use_ema_model,
            device=args.device
        )
        
        # Load and preprocess pointcloud
        print(f"Loading pointcloud from: {args.pointcloud_path}")
        pc_raw = inference_wrapper.load_pointcloud(args.pointcloud_path)
        pc_processed, original_stats = inference_wrapper.preprocess_pointcloud(
            pc_raw, target_num_points=args.target_points
        )
        
        # Run inference
        results = inference_wrapper.infer_grasps(
            pc_processed,
            num_grasps=args.num_grasps,
            condition_type=condition_type,
            class_label=args.class_label,
            region_id=args.region_id
        )
        
        # Filter by confidence
        if args.min_confidence > 0:
            results = inference_wrapper.filter_grasps_by_confidence(
                results, min_confidence=args.min_confidence
            )
        
        # Visualize results
        if args.visualize:
            inference_wrapper.visualize_results(results)
        
        # Save results
        if args.save_results:
            inference_wrapper._save_results_as_mesh(results, args.save_results)
        
        print("Inference completed successfully!")
        
    except Exception as e:
        print(f"Error during inference: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main() 