# Copyright (c) 2024, NVIDIA CORPORATION.  All rights reserved.
#
# NVIDIA CORPORATION and its licensors retain all intellectual property
# and proprietary rights in and to this software, related documentation
# and any modifications thereto.  Any use, reproduction, disclosure or
# distribution of this software and related documentation without an express
# license agreement from NVIDIA CORPORATION is strictly prohibited.

import argparse
import json
import os
import random
import shutil
from pathlib import Path
from typing import List, <PERSON>ple

import h5py
import numpy as np
import trimesh
from tqdm import tqdm


def load_grasp_data(grasp_file_path: Path) -> Tuple[np.ndarray, np.ndarray]:
    """
    Load grasp data from a file.

    This is a placeholder function. You MUST modify this function to fit your
    specific grasp data format. The function should read the file specified
    by `grasp_file_path` and return the grasp transforms and their corresponding
    quality metrics.

    Args:
        grasp_file_path (Path): The path to the file containing grasp data.

    Returns:
        A tuple containing:
        - np.ndarray: An array of grasp transforms, with shape (N, 4, 4),
                      where N is the number of grasps.
        - np.ndarray: An array of grasp qualities, with shape (N,).
    """
    # --- START OF PLACEHOLDER IMPLEMENTATION ---
    # Replace this with your actual data loading logic.
    # Example for a simple CSV file where each row is:
    # qx,qy,qz,qw,x,y,z,quality

    # For demonstration, we generate random data.
    # In your implementation, you would load from the file.
    print(
        f"Warning: Using placeholder grasp data for {grasp_file_path}. "
        f"Please implement your data loading logic in `load_grasp_data`."
    )
    num_grasps = 200
    # Random rotations (quaternions)
    quats = np.random.rand(num_grasps, 4)
    quats /= np.linalg.norm(quats, axis=1)[:, np.newaxis]
    # Random translations
    trans = np.random.rand(num_grasps, 3) - 0.5
    # Random qualities
    qualities = np.random.rand(num_grasps)

    transforms = np.zeros((num_grasps, 4, 4))
    for i in range(num_grasps):
        matrix = trimesh.transformations.quaternion_matrix(quats[i])
        matrix[:3, 3] = trans[i]
        transforms[i] = matrix

    return transforms, qualities
    # --- END OF PLACEHOLDER IMPLEMENTATION ---


def process_object(
    mesh_path: Path,
    grasp_path: Path,
    output_h5_path: Path,
    relative_mesh_path: str,
) -> bool:
    """
    Process a single object: load its mesh and grasp data, and save it
    into an HDF5 file in the format expected by AcronymBaseDataset.

    Args:
        mesh_path (Path): Path to the object's mesh file.
        grasp_path (Path): Path to the object's grasp data file.
        output_h5_path (Path): Path to save the output HDF5 file.
        relative_mesh_path (str): The relative path to the mesh file from the
                                 'meshes' directory.

    Returns:
        bool: True if processing was successful, False otherwise.
    """
    try:
        # Load grasp data using the (to be implemented) loader
        grasp_transforms, grasp_qualities = load_grasp_data(grasp_path)

        if grasp_transforms.shape[0] == 0:
            print(f"Skipping {mesh_path.stem}: No grasps found.")
            return False

        # Load mesh to check for validity, not stored in H5
        mesh = trimesh.load(mesh_path, force="mesh")
        if not isinstance(mesh, trimesh.Trimesh):
            print(f"Skipping {mesh_path.stem}: Not a valid Trimesh object.")
            return False

        # Create the HDF5 file
        with h5py.File(output_h5_path, "w") as hf:
            # Store object metadata
            hf.create_dataset("object/file", data=relative_mesh_path.encode("utf-8"))
            hf.create_dataset("object/scale", data=1.0)  # Assuming uniform scale

            # Store grasp data
            hf.create_dataset("grasps/transforms", data=grasp_transforms)
            # The path for qualities must match exactly
            hf.create_dataset(
                "grasps/qualities/flex/object_material/friction", data=grasp_qualities
            )

        return True
    except Exception as e:
        print(f"Error processing {mesh_path.stem}: {e}")
        return False


def create_split_files(
    h5_files: List[str], output_dir: Path, train_split_ratio: float
):
    """
    Create train/test split JSON files.

    Args:
        h5_files (List[str]): A list of all generated HDF5 filenames.
        output_dir (Path): The root directory for the output dataset.
        train_split_ratio (float): The ratio of the data to be used for training.
    """
    random.shuffle(h5_files)
    num_train = int(len(h5_files) * train_split_ratio)
    train_files = h5_files[:num_train]
    test_files = h5_files[num_train:]

    # The JSON files should contain the basename of the H5 files without extension
    train_basenames = [Path(f).stem for f in train_files]
    test_basenames = [Path(f).stem for f in test_files]

    splits_dir = output_dir / "splits"
    with open(splits_dir / "train.json", "w") as f:
        json.dump({"train": train_basenames}, f)

    with open(splits_dir / "test.json", "w") as f:
        json.dump({"test": test_basenames}, f)

    print(f"Created split files: {len(train_files)} training, {len(test_files)} testing.")


def main(args):
    """
    Main function to run the dataset creation process.
    """
    input_dir = Path(args.input_dir)
    output_dir = Path(args.output_dir)
    train_split_ratio = args.train_split_ratio

    # Validate input directory structure
    input_meshes_dir = input_dir / "meshes"
    input_grasps_dir = input_dir / "grasps"
    if not (input_meshes_dir.is_dir() and input_grasps_dir.is_dir()):
        print(
            "Error: Input directory must contain 'meshes' and 'grasps' subdirectories."
        )
        return

    # Create output directory structure
    output_meshes_dir = output_dir / "meshes"
    output_grasps_dir = output_dir / "grasps"
    output_splits_dir = output_dir / "splits"
    output_meshes_dir.mkdir(parents=True, exist_ok=True)
    output_grasps_dir.mkdir(parents=True, exist_ok=True)
    output_splits_dir.mkdir(parents=True, exist_ok=True)

    print(f"Processing files from: {input_dir}")
    print(f"Saving formatted dataset to: {output_dir}")

    mesh_files = list(input_meshes_dir.glob("*"))
    processed_h5_files = []

    for mesh_path in tqdm(mesh_files, desc="Processing objects"):
        # Find corresponding grasp file (assuming same stem)
        # You might need to change the grasp file extension
        grasp_file_stem = mesh_path.stem
        possible_grasp_files = list(input_grasps_dir.glob(f"{grasp_file_stem}.*"))
        if not possible_grasp_files:
            print(f"Warning: No grasp file found for {mesh_path.name}")
            continue
        grasp_path = possible_grasp_files[0]

        # Define output paths
        output_h5_filename = f"{mesh_path.stem}.h5"
        output_h5_path = output_grasps_dir / output_h5_filename
        output_mesh_path = output_meshes_dir / mesh_path.name

        # Copy mesh file to the new structure
        shutil.copy(mesh_path, output_mesh_path)
        relative_mesh_path = f"../meshes/{mesh_path.name}"

        # Process the object and create HDF5 file
        success = process_object(
            mesh_path, grasp_path, output_h5_path, relative_mesh_path
        )
        if success:
            processed_h5_files.append(output_h5_filename)

    # Create train/test splits
    if processed_h5_files:
        create_split_files(processed_h5_files, output_dir, train_split_ratio)
    else:
        print("No objects were processed, skipping split file creation.")

    print("\nDataset creation finished.")
    print("Next steps:")
    print(
        "1. IMPORTANT: Implement your custom data loading logic in the "
        "`load_grasp_data` function in this script."
    )
    print(
        "2. Run this script with your data to generate the HDF5 dataset."
    )
    print(
        "3. Update your training config (.yaml) to point to the generated "
        f"dataset root directory: '{output_dir}'"
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Create a custom dataset in the Acronym format for graspLDM."
    )
    parser.add_argument(
        "--input_dir",
        type=str,
        required=True,
        help="Path to the directory containing your raw 'meshes' and 'grasps' data.",
    )
    parser.add_argument(
        "--output_dir",
        type=str,
        required=True,
        help="Path to the root directory where the formatted dataset will be saved.",
    )
    parser.add_argument(
        "--train_split_ratio",
        type=float,
        default=0.8,
        help="The ratio of data to be used for the training set (default: 0.8).",
    )
    args = parser.parse_args()
    main(args)

# --- USAGE EXAMPLE ---
#
# 1. Organize your data into the following structure:
#    /path/to/your/raw_data/
#    ├── meshes/
#    │   ├── object_01.obj
#    │   └── object_02.stl
#    └── grasps/
#        ├── object_01.csv
#        └── object_02.txt
#
# 2. IMPORTANT:
#    Modify the `load_grasp_data` function in this script to correctly
#    read your specific grasp file format (e.g., .csv, .txt, .json).
#
# 3. Run the script from your terminal:
#    python tools/create_custom_dataset.py \
#      --input_dir /path/to/your/raw_data \
#      --output_dir /path/to/your/formatted_dataset
#
# 4. This will create the following structure:
#    /path/to/your/formatted_dataset/
#    ├── meshes/
#    │   ├── object_01.obj
#    │   └── object_02.stl
#    ├── grasps/
#    │   ├── object_01.h5
#    │   └── object_02.h5
#    └── splits/
#        ├── train.json
#        └── test.json
