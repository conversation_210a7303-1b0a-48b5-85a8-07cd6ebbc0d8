# Simple data conversion script - convert_data.py
import numpy as np
import torch
import trimesh
import argparse
from pathlib import Path

def convert_pointcloud_to_acronym_format(pc_path, output_dir):
    """
    Convert your point cloud to ACRONYM compatible format.
    
    Args:
        pc_path: Path to your point cloud file (.ply, .pcd, .xyz, .npy, etc.)
        output_dir: Output directory
        
    Returns:
        Path: Path to the converted file
    """
    print(f"Converting point cloud: {pc_path}")
    
    # Load your point cloud data based on file extension
    if pc_path.endswith('.npy'):
        pc = np.load(pc_path)
    elif pc_path.endswith('.ply'):
        mesh = trimesh.load(pc_path)
        if hasattr(mesh, 'vertices'):
            pc = np.array(mesh.vertices)
        else:
            # Handle point cloud PLY files
            pc = np.array(mesh.vertices) if hasattr(mesh, 'vertices') else mesh.points
    elif pc_path.endswith('.pcd'):
        # For PCD files, try to use open3d
        try:
            import open3d as o3d
            pcd = o3d.io.read_point_cloud(pc_path)
            pc = np.asarray(pcd.points)
        except ImportError:
            raise ImportError("open3d is required for .pcd files. Install with: pip install open3d")
    elif pc_path.endswith('.xyz'):
        pc = np.loadtxt(pc_path)
    elif pc_path.endswith('.txt'):
        # Try to load as space-separated coordinates
        pc = np.loadtxt(pc_path)
    else:
        raise ValueError(f"Unsupported file format: {pc_path}")
    
    # Ensure point cloud format is [N, 3]
    if pc.ndim != 2 or pc.shape[1] != 3:
        raise ValueError(f"Point cloud should be [N, 3], got shape {pc.shape}")
    
    print(f"Loaded point cloud with {pc.shape[0]} points")
    
    # Create fake mesh object (for compatibility with ACRONYM dataset loader)
    fake_mesh = trimesh.Trimesh(vertices=pc, faces=[])
    
    # Save in ACRONYM format
    object_name = Path(pc_path).stem
    output_path = Path(output_dir) / "test" / f"{object_name}.ply"
    output_path.parent.mkdir(parents=True, exist_ok=True)
    fake_mesh.export(str(output_path))
    
    print(f"Converted point cloud saved to: {output_path}")
    return output_path

def create_acronym_dataset_structure(output_dir, pc_files):
    """
    Create ACRONYM-like dataset structure for multiple point cloud files.
    
    Args:
        output_dir: Base output directory
        pc_files: List of point cloud file paths
        
    Returns:
        List: Paths to converted files
    """
    converted_files = []
    
    for pc_file in pc_files:
        converted_path = convert_pointcloud_to_acronym_format(pc_file, output_dir)
        converted_files.append(converted_path)
    
    print(f"\nDataset structure created in: {output_dir}")
    print(f"Converted {len(converted_files)} files")
    
    return converted_files

def main():
    parser = argparse.ArgumentParser(description="Convert point clouds to ACRONYM format")
    parser.add_argument("--input", type=str, required=True, 
                       help="Input point cloud file or directory")
    parser.add_argument("--output", type=str, required=True,
                       help="Output directory for ACRONYM format")
    parser.add_argument("--recursive", action="store_true",
                       help="Recursively search for point cloud files in input directory")
    
    args = parser.parse_args()
    
    input_path = Path(args.input)
    output_dir = Path(args.output)
    
    if input_path.is_file():
        # Single file conversion
        convert_pointcloud_to_acronym_format(str(input_path), str(output_dir))
    elif input_path.is_dir():
        # Directory conversion
        extensions = ['.npy', '.ply', '.pcd', '.xyz', '.txt']
        pc_files = []
        
        if args.recursive:
            for ext in extensions:
                pc_files.extend(input_path.rglob(f"*{ext}"))
        else:
            for ext in extensions:
                pc_files.extend(input_path.glob(f"*{ext}"))
        
        if not pc_files:
            print(f"No point cloud files found in {input_path}")
            return
        
        print(f"Found {len(pc_files)} point cloud files")
        create_acronym_dataset_structure(str(output_dir), [str(f) for f in pc_files])
    else:
        raise ValueError(f"Input path does not exist: {input_path}")

if __name__ == "__main__":
    main()