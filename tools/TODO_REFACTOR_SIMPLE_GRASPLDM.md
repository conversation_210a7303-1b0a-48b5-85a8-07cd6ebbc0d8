### GraspLDM 推理引擎重构路线图 (v2.0)

> **目标:** 将 `SimpleGraspLDMInference` God-Class 重构为一个模块化、可维护、可扩展且高性能的抓取推理引擎，使其更适合于实时机器人应用。
>
> **优先级说明:**
> - **[P0-关键]:** 重构的核心，必须完成。
> - **[P1-重要]:** 显著提升代码质量、可用性和鲁棒性。
> - **[P2-推荐]:** 进一步的改进和优化，建议实施。
> - **[P3-探索]:** 前沿或复杂的优化，可在核心功能稳定后进行研究。

---

### 第 1 阶段：核心架构重构 (Core Architecture Refactoring)

- [ ] **[P0-关键]** 目录结构初始化
  - [ ] 新建 `grasp_inference/` 包目录，包含 `__init__.py`。
  - [ ] `grasp_inference/core/`: 核心逻辑模块
    - [ ] `point_cloud_processor.py`: 点云处理 (原 `_prepare_pointcloud` 等)。
    - [ ] `grasp_generator.py`: LDM 抓取生成。
    - [ ] `grasp_evaluator.py`: 抓取评估策略基类与实现。
    - [ ] `grasp_refiner.py`: 抓取提纯策略基类与实现。
  - [ ] `grasp_inference/utils/`: 工具类
    - [ ] `coordinate_transformer.py`: 坐标系转换。
    - [ ] `visualizer.py`: 可视化后端基类与实现 (`trimesh`, `pyrender`)。
  - [ ] `grasp_inference/datamodels/`: 数据结构
    - [ ] `grasp_result.py`: 定义 `GraspResult` 数据类。
  - [ ] `grasp_inference/api.py`: 对外暴露的 Facade。
  - [ ] `grasp_inference/exceptions.py`: 自定义异常。
  - [ ] `grasp_inference/configs/`: 默认配置文件。

- [ ] **[P0-关键]** 核心组件拆分
  - [ ] `PointCloudProcessor`: 实现 `process(pc, camera_pose)` 方法，处理点云的转换、正则化和归一化。
  - [ ] `GraspGenerator`: 封装 LDM 模型加载与推理，提供 `generate(pc_features)` 接口。
  - [ ] `GraspEvaluator`: 定义 `BaseEvaluator` 抽象基类 (`score(pc, grasps)`)，并实现 `SixDOFGraspEvaluator`。
  - [ ] `GraspRefiner`: 定义 `BaseRefiner` 接口 (`refine(pc, grasps)`)，并拆分出 `GradientRefiner` 和 `SamplingRefiner`。
  - [ ] `CoordinateTransformer`: 统一处理 `denormalized <-> centered <-> world` 坐标转换。
  - [ ] `GraspVisualizer`: 定义 `BaseVisualizer` (`show`, `save`)，并迁移现有可视化代码。
  - [ ] `GraspResult`: 实现包含 `grasps`, `scores`, `point_cloud` 等字段的数据类，并提供 `__getitem__` 兼容旧 `dict` 访问。
  
- [ ] **[P0-关键]** `GraspPipeline` (新 Facade)
  - [ ] 在 `grasp_inference/api.py` 中创建 `GraspPipeline` 类。
  - [ ] 组合上述所有组件，本身应为无状态。
  - [ ] 重写 `run(pointcloud, ...)` 方法，仅做流程编排，不实现任何具体逻辑。

---

### 第 2 阶段：架构与质量改进 (Architecture & Quality Improvements)

- [ ] **[P1-重要]** 配置管理
  - [ ] 引入 `Hydra` 和 `OmegaConf` 管理所有可配置参数（模型路径、步数、阈值等）。
  - [ ] 将原 `__init__` 中的大量参数迁移至 `default.yaml` 配置文件。
  - [ ] `GraspPipeline` 提供 `from_config(cfg_path)` 或 `from_hydra(cfg_name)` 工厂方法进行初始化。

- [ ] **[P1-重要]** 错误与异常处理
  - [ ] 在 `grasp_inference/exceptions.py` 中定义 `BaseGraspException`。
  - [ ] 创建具体的异常类，如 `PointCloudError`, `InferenceError`, `ConfigurationError`, `VisualizationError`。
  - [ ] 在所有模块中，使用这些自定义异常替换通用的 `ValueError` 或 `RuntimeError`。

- [ ] **[P1-重要]** 日志系统
  - [ ] 全面引入 `logging` 模块，替换所有 `print()` 语句。
  - [ ] 支持 `DEBUG`, `INFO`, `WARNING`, `ERROR` 不同日志级别。
  - [ ] 在日志中增加关键节点（如模型加载、生成、提纯）的耗时信息。

---

### 第 3 阶段：API 设计与可用性 (API Design & Usability)

- [ ] **[P2-推荐]** 流畅接口 (Fluent API)
  - [ ] 探索为 `GraspPipeline` 实现链式调用，提高可读性。
  - [ ] 示例: `pipeline.load_pc(pc).generate().refine().filter_by_score(0.8).get_best_grasp()`。

- [ ] **[P1-重要]** 向后兼容
  - [ ] 在 `tools/simple_inference.py` 中保留 `SimpleGraspLDMInference`。
  - [ ] 将其内部实现改为代理到新的 `GraspPipeline`。
  - [ ] 对所有被弃用的参数和方法，使用 `warnings.warn` 并发出 `DeprecationWarning`。

---

### 第 4 阶段：性能与优化 (Performance & Optimization)

- [ ] **[P1-重要]** 性能基准测试
  - [ ] 建立端到端的性能基准测试，覆盖不同输入点云大小和抓取数量。
  - [ ] 对比重构前后各阶段的运行时间，确保无性能退化。

- [ ] **[P2-推荐]** 算法优化
  - [ ] 评估点云处理（如最远点采样、K近邻搜索）的性能瓶颈。
  - [ ] 探索使用 `torch-geometric` 或其他库进行加速。

- [ ] **[P3-探索]** 模型加速与量化
  - [ ] 研究使用 `torch.jit.script` 或 `TensorRT` 对 LDM 和评估器模型进行编译加速。
  - [ ] 研究使用 ONNX 运行时和模型量化（FP16/INT8）来降低延迟和内存占用，特别关注对精度影响的评估。

---

### 第 5 阶段：测试、文档与生态 (Testing, Docs & Ecosystem)

- [ ] **[P1-重要]** 单元与集成测试
  - [ ] 为每个新模块 (`PointCloudProcessor`, `GraspGenerator` 等) 编写独立的单元测试 (pytest)。
  - [ ] 编写集成测试，验证 `GraspPipeline` 的端到端流程正确性。
  - [ ] 在 GitHub Actions CI 中自动运行所有测试。

- [ ] **[P1-重要]** 文档与代码质量
  - [ ] 强制要求所有公共 API (函数、类、方法) 都有完整的 Google 风格 Docstring 和类型提示 (Type Hinting)。
  - [ ] 使用 `mypy` 进行静态类型检查。
  - [ ] 使用 `Sphinx` 或 `MkDocs` 自动从代码生成 API 文档网站。
  - [ ] 更新项目 `README.md`，包含新架构的说明和使用示例。

- [ ] **[P1-重要]** 示例与 Demo
  - [ ] 更新 `examples/` 目录下的所有示例，使用新的 `GraspPipeline` API。
  - [ ] 创建一个Jupyter Notebook，详细演示新 API 的使用方法，并对比新旧输出的一致性。

- [ ] **[P2-推荐]** 机器人系统集成
  - [ ] 创建一个 `ros/` 或 `ros2/` 目录。
  - [ ] 实现一个 ROS/ROS2 节点，该节点订阅 `sensor_msgs/PointCloud2` 话题，运行 `GraspPipeline`，并将结果发布到自定义的抓取消息话题。

---

> 完成以上任务后，可将此文件迁移至项目看板（如 GitHub Projects）进行追踪。 