from __future__ import print_function

import numpy as np
import trimesh
from utils import utils, sample

def get_color_plasma(x):
    """Returns a color from the plasma colormap."""
    import matplotlib.pyplot as plt
    color = plt.cm.plasma(x)[:3]  # Get RGB values
    return tuple(color)  # Return RGB values in 0-1 range


def draw_scene(pc=None,
               grasps=[],
               grasp_scores=None,
               grasp_color=None,
               gripper_color=(0, 1, 0),
               mesh=None,
               show_gripper_mesh=False,
               grasps_selection=None,
               visualize_diverse_grasps=False,
               min_seperation_distance=0.03,
               pc_color=None,
               plasma_coloring=False,
               target_cps=None,
               save_path=None,
               show_scene=False):
    """
    Draws a 3D scene using Trimesh, with options to save to a file.
    
    Args:
        pc (np.ndarray): Point cloud of the object (Nx3).
        grasps (list): List of 4x4 numpy arrays for grasp transformations.
        grasp_scores (list): Scores for coloring grasps.
        grasp_color (tuple or list): Color for grasps. Can be a single RGB tuple (0-1 range)
            or a list of RGB tuples for each grasp.
        gripper_color (tuple): Default RGB color for the gripper (0-1 range).
        mesh (trimesh.Trimesh or list): Object mesh(es) to display.
        show_gripper_mesh (bool): If True, shows full gripper mesh; otherwise, shows lines.
        grasps_selection (list): Binary list to filter grasps.
        visualize_diverse_grasps (bool): If True, filters grasps for diversity.
        min_seperation_distance (float): Minimum distance for diverse grasps.
        pc_color (np.ndarray): Nx3 color for each point in the cloud (0-1 range).
        plasma_coloring (bool): If True, use plasma colormap for the point cloud.
        target_cps (np.ndarray): Target contact points to visualize.
        save_path (str, optional): If provided, saves the scene to this file path.
        show_scene (bool): If True, displays the scene in an interactive window.
    """
    scene = trimesh.Scene()

    # Add object mesh to the scene
    if mesh is not None:
        if isinstance(mesh, list):
            for m in mesh:
                scene.add_geometry(m)
        elif isinstance(mesh, trimesh.Trimesh):
            scene.add_geometry(mesh)

    # Add point cloud to the scene
    if pc is not None:
        if pc_color is None:
            if plasma_coloring:
                # Normalize z-coordinates to [0, 1] for colormap 
                z_normalized = pc[:, 2]  # Use z-coordinate directly 
                import matplotlib.pyplot as plt
                colors = plt.cm.plasma(z_normalized)[:, :3] * 255  # Convert to 0-255 for Trimesh
            else:
                colors = (25, 25, 255, 255)  # Default blue color (0.1, 0.1, 1) * 255
        else:
            # Convert 0-1 range colors to 0-255 range for Trimesh
            if pc_color.max() <= 1.0:
                colors = (pc_color * 255).astype(np.uint8)
            else:
                colors = pc_color
        point_cloud = trimesh.points.PointCloud(pc, colors=colors)
        scene.add_geometry(point_cloud)

    # Grasp processing logic
    max_grasps = 100
    grasps = np.array(grasps)

    if grasp_scores is not None:
        grasp_scores = np.array(grasp_scores)

    # Downsampling logic
    if len(grasps) > max_grasps:
        print('Downsampling grasps, there are too many')
        chosen_ones = np.random.randint(low=0, high=len(grasps), size=max_grasps)
        grasps = grasps[chosen_ones]
        if grasp_scores is not None:
            grasp_scores = grasp_scores[chosen_ones]

    # Sort by score
    if grasp_scores is not None:
        indexes = np.argsort(-np.asarray(grasp_scores))
    else:
        indexes = range(len(grasps))

    print('draw scene ', len(grasps))

    # Define gripper geometry
    gripper_mesh = None
    if show_gripper_mesh:
        try:
            # gripper_mesh = trimesh.load_mesh('assets/gripper.obj')
            gripper_mesh = sample.Object('gripper_models/panda_gripper.obj').mesh
        except Exception as e:
            print(f"Warning: Could not load 'gripper_models/panda_gripper.obj'. Using line representation. Error: {e}")
            show_gripper_mesh = False

    if not show_gripper_mesh:
        # gripper geometry
        grasp_pc = np.squeeze(utils.get_control_point_tensor(1, False), 0)
        grasp_pc[2, 2] = 0.059
        grasp_pc[3, 2] = 0.059

        mid_point = 0.5 * (grasp_pc[2, :] + grasp_pc[3, :])

        modified_grasp_pc = []
        modified_grasp_pc.append(np.zeros((3, ), np.float32))
        modified_grasp_pc.append(mid_point)
        modified_grasp_pc.append(grasp_pc[2])
        modified_grasp_pc.append(grasp_pc[4])
        modified_grasp_pc.append(grasp_pc[2])
        modified_grasp_pc.append(grasp_pc[3])
        modified_grasp_pc.append(grasp_pc[5])

        grasp_pc = np.asarray(modified_grasp_pc)

    selected_grasps_so_far = []
    removed = 0

    # Score statistics
    if grasp_scores is not None:
        min_score = np.min(grasp_scores)
        max_score = np.max(grasp_scores)
        top5 = np.array(grasp_scores).argsort()[-5:][::-1]

    # Add grasps to the scene
    for ii in range(len(grasps)):
        i = indexes[ii]
        if grasps_selection is not None:
            if grasps_selection[i] == False:
                continue

        g = grasps[i]
        is_diverse = True
        for prevg in selected_grasps_so_far:
            distance = np.linalg.norm(prevg[:3, 3] - g[:3, 3])

            if distance < min_seperation_distance:
                is_diverse = False
                break

        if visualize_diverse_grasps:
            if not is_diverse:
                removed += 1
                continue
            else:
                if grasp_scores is not None:
                    print('selected', i, grasp_scores[i], min_score, max_score)
                else:
                    print('selected', i)
                selected_grasps_so_far.append(g)

        # Color assignment
        current_color = gripper_color
        if isinstance(gripper_color, list):
            pass
        elif grasp_scores is not None:
            normalized_score = (grasp_scores[i] - min_score) / (max_score - min_score + 0.0001)
            if grasp_color is not None:
                current_color = grasp_color[ii]
            else:
                current_color = get_color_plasma(normalized_score)

            if min_score == 1.0:
                current_color = (0.0, 1.0, 0.0)

        # Convert RGB 0-1 to RGBA 0-255 for Trimesh
        if isinstance(current_color, tuple) and len(current_color) == 3:
            current_color_255 = tuple(int(c * 255) for c in current_color) + (255,)
        elif isinstance(current_color, list) and isinstance(current_color[0], tuple):
            current_color_255 = tuple(int(c * 255) for c in current_color[i]) + (255,)
        else:
            current_color_255 = current_color

        if show_gripper_mesh and gripper_mesh:
            gripper_transformed = gripper_mesh.copy()
            gripper_transformed.apply_transform(g)
            gripper_transformed.visual.face_colors = current_color_255
            scene.add_geometry(gripper_transformed)
        else:
            # Transform points and create continuous line path for the gripper
            pts = np.matmul(grasp_pc, g[:3, :3].T)
            pts += np.expand_dims(g[:3, 3], 0)
            
            # Create a continuous line path connecting all control points
            # This mimics the behavior of mlab.plot3d from the old version
            path = trimesh.path.Path3D(
                entities=[trimesh.path.entities.Line(np.arange(len(pts)))],
                vertices=pts
            )
            
            # Set colors and line width for the path
            path.colors = np.array([current_color_255], dtype=np.uint8)
            
            
            scene.add_geometry(path)

        if target_cps is not None:
            cps_cloud = trimesh.points.PointCloud(target_cps[ii], colors=(255, 0, 0, 255))
            scene.add_geometry(cps_cloud)

    print('removed {} similar grasps'.format(removed))

    # Save or show the scene
    if save_path:
        try:
            scene.export(save_path)
            print(f"Scene saved to {save_path}")
        except Exception as e:
            print(f"Failed to save scene: {e}")

    if show_scene:
        scene.show()

    return scene
