------------ Options -------------
allowed_categories: 
arch: vae
balanced_data: False
batch_size: 192
beta1: 0.9
blacklisted_categories: 
checkpoints_dir: ./checkpoints
confidence_weight: 1.0
continue_train: False
dataset_root_folder: /home/<USER>/datasets/unified_grasp_data/
dataset_split: train
depth_noise: 0.0
epoch_count: 1
gpu_ids: [0]
grasp_transfer: False
grasp_transfer_weight: 1.0
grasps_folder_name: grasps
grasps_ratio: 1.0
gripper: panda
gripper_pc_npoints: -1
init_gain: 0.02
init_type: normal
is_train: True
kl_loss_weight: 0.01
latent_size: 2
lr: 0.0002
lr_decay_iters: 50
lr_policy: lambda
max_dataset_size: inf
merge_pcs_in_vae_encoder: 0
model_scale: 1
name: vae_pretrained
niter: 1000
niter_decay: 10000
no_vis: False
npoints: 1024
num_grasp_clusters: 32
num_grasps_per_object: 192
num_objects_per_batch: 1
num_threads: 3
occlusion_dropout_rate: 0
occlusion_nclusters: 0
phase: train
pointnet_nclusters: 128
pointnet_radius: 0.02
print_freq: 100
run_test_freq: 10
save_epoch_freq: 50
save_latest_freq: 250
seed: None
serial_batches: False
skip_error: False
splits_folder_name: splits
use_uniform_quaternions: 0
verbose_plot: False
which_epoch: latest
-------------- End ----------------
