from typing import Any, <PERSON>ple

import torch
import torch.nn.functional as F
from diffusers import <PERSON>IM<PERSON>cheduler, DDPMScheduler
from torch import Tensor, nn
from tqdm.auto import tqdm


class GaussianDiffusion1D(nn.Module):
    """
    Gaussian Diffusion Model for 1D Data
    
    PIPELINE OVERVIEW:
    ==================
    Training Pipeline:
        x_0 [B,C,D] → Sample random t → Add noise q(x_t|x_0) → x_t [B,C,D] 
        → Model prediction ε_θ(x_t, t, z_cond) → Loss(ε, ε_θ) → Backprop
    
    Inference Pipeline:
        x_T [B,C,D] (pure noise) → Iterative denoising for t=T→0 
        → Model prediction ε_θ(x_t, t, z_cond) → Remove noise step 
        → x_{t-1} [B,C,D] → ... → x_0 [B,C,D] (clean sample)
    
    TENSOR DIMENSIONS:
    ==================
    - B: Batch size
    - C: Channels (typically 1 for 1D data)
    - D: Feature dimensions (n_dims)
    - Input data x_0: [B, C, D]
    - Conditioning z_cond: [B, ...] (flexible conditioning dimensions)
    - Timesteps t: [B] or [B, 1] (batch of timestep indices)
    - Model output: [B, C, D] for fixed variance, [B, 2C, D] for learned variance
    
    MATHEMATICAL FOUNDATION:
    ========================
    Forward process: q(x_t|x_0) = N(x_t; √(α̅_t) x_0, (1-α̅_t)I)
    Reverse process: p_θ(x_{t-1}|x_t) = N(x_{t-1}; μ_θ(x_t,t), Σ_θ(x_t,t))
    """
    
    ALL_LOSSES = ["l1", "l2", "huber"]
    NOISE_SCHEDULERS = ["ddpm", "ddim"]
    BETA_SCHEDULES = ["linear", "scaled_linear", "squaredcos_cap_v2", "cosine"]
    VARIANCE_TYPES = [
        "fixed_small",
        "fixed_small_log",
        "fixed_large",
        "fixed_large_log",
        "learned",
        "learned_range",
    ]

    def __init__(
        self,
        model: nn.Module,
        n_dims: int,
        noise_scheduler_type: str = "ddpm",
        beta_schedule: str = "linear",
        variance_type: str = "fixed_small",
        pred_type: str = "epsilon",
        beta_start: str = 0.0001,
        beta_end: str = 0.02,
        num_steps: int = 1000,
        loss_type: str = "l1",
        clip_sample=True,
    ) -> None:
        """Initialize Gaussian Diffusion 1D pipeline

        Args:
            model (nn.Module): Denoising neural network model
                Expected signature: forward(x, time=t, z_cond=z_cond)
                Input/Output dimensions:
                - x: [B,C,D] → [B,C,D] (fixed var) or [B,2C,D] (learned var)
                - time: [B] timestep indices
                - z_cond: [B,...] optional conditioning tensor

            n_dims (int): Feature dimensions (D) of input data
                Determines the size of the last dimension in tensors [B,C,D]

            noise_scheduler_type (str): Type of noise scheduler for diffusion steps
                - "ddpm": Denoising Diffusion Probabilistic Models
                - "ddim": Denoising Diffusion Implicit Models (faster sampling)

            beta_schedule (str): Noise variance schedule over timesteps
                Controls how noise is added during forward diffusion

            variance_type (str): How to handle variance in reverse process
                - "fixed_*": Use predetermined variance values
                - "learned*": Model predicts variance (requires 2C output channels)

            pred_type (str): What the model predicts
                - "epsilon": Predict the noise ε that was added
                - "sample": Directly predict the denoised sample
                - "v_prediction": Predict v-parameterization (https://imagen.research.google/video/paper.pdf)

            num_steps (int): Total diffusion timesteps T (typically 1000)
                Higher values = smoother diffusion but slower training/sampling

            loss_type (str): Training loss function type
                Applied between true noise and predicted noise
        """
        super().__init__()
        # Store core diffusion parameters
        self.num_train_timesteps = num_steps
        self.beta_start = beta_start
        self.beta_end = beta_end
        self.beta_schedule = (
            beta_schedule if not beta_schedule == "cosine" else "squaredcos_cap_v2"
        )
        self.variance_type = variance_type
        self.num_steps = num_steps
        self.pred_type = pred_type
        self.clip_sample = clip_sample

        # Core model and dimension configuration
        self.model = model  # Denoising network: [B,C,D] → [B,C,D] or [B,2C,D]
        self.n_dims = n_dims  # Feature dimension D
        self.channels = 1  # Channel dimension C (fixed to 1 for 1D data)

        # Initialize noise scheduler for diffusion process
        self.noise_scheduler = self.configure_noise_scheduler(noise_scheduler_type)
        self._noise_scheduler_type = noise_scheduler_type

        # Validate and set loss function
        assert (
            loss_type in self.ALL_LOSSES
        ), f"Invalid loss_type. Supported losses: {self.ALL_LOSSES} "
        self.loss_type = loss_type

        # Configure variance prediction mode
        self._is_variance_learned = self.variance_type in ["learned", "learned_range"]
        if self._is_variance_learned:
            # Model outputs both noise and variance: [B,C,D] → [B,2C,D]
            assert (
                self.model.out_channels == 2
            ), f"For learned variance mode ({self.variance_type}), the score model should 2 output channels: eps_pred and var_pred"
        else:
            # Model outputs only noise prediction: [B,C,D] → [B,C,D]
            assert (
                self.model.out_channels == 1
            ), f"For pre-defined variance type {self.variance_type}, the score model should have only one output channel: eps_pred"

    @property
    def num_inference_steps(self):
        """Get number of inference steps for sampling
        
        Returns:
            int: Number of denoising steps during inference
                May be different from training steps for faster sampling
        """
        inf_t = self.noise_scheduler.num_inference_steps
        return inf_t if inf_t is not None else self.num_steps

    def set_inference_timesteps(self, num_steps):
        """Configure custom timesteps for inference sampling
        
        Useful for accelerated sampling with fewer steps than training, (DDIM)
        
        Args:
            num_steps (int): Number of denoising steps for inference
                Fewer steps = faster sampling but potentially lower quality
        """
        self.noise_scheduler.set_timesteps(num_steps)

    def configure_noise_scheduler(self, noise_scheduler_type: str) -> Any:
        """Initialize and configure the noise scheduler for diffusion process
        
        The scheduler handles:
        - Forward process: How to add noise at each timestep
        - Reverse process: How to denoise at each timestep
        - Beta schedule: Variance schedule over time
        
        Args:
            noise_scheduler_type (str): Type of scheduler ("ddpm" or "ddim")
            
        Returns:
            Scheduler: Configured diffusion scheduler object
        """
        # Validate scheduler configuration
        assert (
            noise_scheduler_type in self.NOISE_SCHEDULERS
        ), f"{self.noise_scheduler} Not supported"
        assert (
            self.beta_schedule in self.BETA_SCHEDULES
        ), f"{self.beta_schedule} not supported"
        assert (
            self.variance_type in self.VARIANCE_TYPES
        ), f"{self.variance_type} not supported"

        # Common scheduler configuration
        kwargs = dict(
            num_train_timesteps=self.num_steps,
            beta_start=self.beta_start,
            beta_end=self.beta_end,
            beta_schedule=self.beta_schedule,
            variance_type=self.variance_type,
            prediction_type=self.pred_type,
            clip_sample=self.clip_sample,
        )

        # Initialize specific scheduler type
        if noise_scheduler_type == "ddpm":
            scheduler = DDPMScheduler(**kwargs)
        elif noise_scheduler_type == "ddim":
            # DDIM doesn't use variance_type parameter
            kwargs.pop("variance_type")
            scheduler = DDIMScheduler(**kwargs)
        else:
            raise NotImplementedError

        return scheduler

    def q_sample_at_t(self, x_0: Tensor, t: int) -> Tuple[Tensor, Tensor]:
        """Forward diffusion: Add noise to clean data at timestep t
        
        Implements q(x_t|x_0) = N(x_t; √(α̅_t) x_0, (1-α̅_t)I)
        This is the forward process that gradually corrupts data with Gaussian noise
        
        DIMENSION FLOW:
        x_0 [B,C,D] → noise [B,C,D] → x_t [B,C,D]
        
        Args:
            x_0 (Tensor): Clean input data at t=0
                Shape: [B, C, D] where B=batch, C=channels, D=features
            t (int): Timestep index (0 ≤ t < num_steps)
                Higher t = more noise added
                
        Returns:
            Tuple[Tensor, Tensor]: 
                - x_t: Noisy data at timestep t [B,C,D]
                - noise: The Gaussian noise that was added [B,C,D]
        """
        # Sample Gaussian noise with same shape as input
        noise = torch.randn_like(x_0)  # [B,C,D]
        
        # Apply noise scheduler to get noisy sample at timestep t
        noisy_x_t = self.noise_scheduler.add_noise(x_0, noise, t)  # [B,C,D]
        
        return noisy_x_t, noise

    def loss_fn(self, true_noise: Tensor, predicted_noise: Tensor) -> Tensor:
        """Compute training loss between true and predicted noise
        
        This is the core training objective: learn to predict the noise
        that was added during the forward diffusion process
        
        DIMENSION FLOW:
        true_noise [B,C,D] + predicted_noise [B,C,D] → loss [1]
        
        Args:
            true_noise (Tensor): Ground truth noise from forward process [B,C,D]
            predicted_noise (Tensor): Model's noise prediction [B,C,D]
            
        Returns:
            Tensor: Scalar loss value for backpropagation
        """
        if self.loss_type == "l1":
            loss = F.l1_loss(true_noise, predicted_noise)
        elif self.loss_type == "l2":
            loss = F.mse_loss(true_noise, predicted_noise)
        elif self.loss_type == "huber":
            loss = F.smooth_l1_loss(true_noise, predicted_noise)
        else:
            raise NotImplementedError()

        return loss

    def forward(self, x_0: Tensor, z_cond: Tensor = None, **kwargs) -> Tensor:
        """Training forward pass: Compute diffusion loss
        
        TRAINING PIPELINE:
        1. Sample random timestep t for each batch element
        2. Add noise to x_0 to get x_t (forward diffusion)
        3. Predict noise using model: ε_θ(x_t, t, z_cond)
        4. Compute loss between true noise and predicted noise
        
        DIMENSION FLOW:
        x_0 [B,C,D] → x_t [B,C,D] → model → pred_noise [B,C,D] → loss [1]
        z_cond [B,...] → model (conditioning)
        t [B] → model (timestep conditioning)
        
        Args:
            x_0 (Tensor): Clean training data samples
                Shape: [B,C,D] where D must equal self.n_dims
            z_cond (Tensor, optional): Conditioning information
                Shape: [B,...] flexible conditioning dimensions
                Examples: class labels, text embeddings, etc.
                
        Returns:
            Tensor: Training loss scalar for optimization
        """
        b, _, d = x_0.shape
        
        # Validate input dimensions match expected feature size
        assert (
            d == self.n_dims
        ), f"Got tensor with size {d} at index -1, expected {self.n_dims} from self.n_dims."

        # Sample random timesteps for each batch element
        t = torch.randint(0, self.num_steps, (b,), device=x_0.device).long()  # [B]

        # Apply forward diffusion: add noise to get x_t
        x_t, true_noise = self.q_sample_at_t(x_0, t)  # x_t: [B,C,D], noise: [B,C,D]

        # Model prediction: denoise x_t conditioned on timestep and optional conditioning
        out = self.model(x_t, time=t, z_cond=z_cond, **kwargs)  # [B,C,D] or [B,2C,D]

        # Handle variance prediction mode
        if self._is_variance_learned:
            # Split output into noise and variance predictions
            noise_pred, var_pred = out.chunk(2, dim=1)  # Each: [B,C,D]
        else:
            # Model only predicts noise
            noise_pred = out  # [B,C,D]

        # Compute training loss between true and predicted noise
        loss = self.loss_fn(true_noise, noise_pred)  # [1]

        return loss

    @torch.no_grad()
    def sample(
        self,
        z_cond: Tensor = None,
        batch_size: int = 1,
        return_all: bool = False,
        device: torch.device = "cuda:0" if torch.cuda.is_available() else "cpu",
        **kwargs,
    ) -> Tuple[Tensor, Tensor]:
        """Inference sampling: Generate samples via reverse diffusion
        
        SAMPLING PIPELINE:
        1. Start with pure Gaussian noise x_T ~ N(0,I)
        2. Iteratively denoise for t = T, T-1, ..., 1, 0
        3. At each step: predict noise ε_θ(x_t, t, z_cond)
        4. Remove predicted noise to get x_{t-1}
        5. Return final clean sample x_0
        
        DIMENSION FLOW:
        x_T [B,C,D] (pure noise) → ... → x_0 [B,C,D] (clean sample)
        z_cond [B,...] → model (conditioning at each step)
        
        Args:
            z_cond (Tensor, optional): Conditioning for generation
                Shape: [B,...] must match batch_size if provided
            batch_size (int): Number of samples to generate
                Determines B dimension of output [B,C,D]
            return_all (bool): Whether to return intermediate denoising steps
                If True, returns trajectory of all x_t states
            device (torch.device): Device for tensor computations
            
        Returns:
            Tuple[Tensor, List[Tensor]]:
                - x_0: Final clean samples [B,C,D]
                - all_noisy: List of intermediate states if return_all=True
                  Each element: [B,C,D] representing x_t at each timestep
        """
        # Initialize with pure Gaussian noise
        x_T = torch.randn((batch_size, self.channels, self.n_dims)).to(device)  # [B,C,D]

        x_noisy = x_T  # Current state in denoising process
        all_noisy = [x_T] if return_all else []  # Trajectory storage

        # Reverse diffusion: iteratively denoise from T to 0
        for t in tqdm(
            reversed(
                range(
                    0, self.num_steps, int(self.num_steps // self.num_inference_steps)
                )
            ),
            desc="Sampling time step",
            total=self.num_inference_steps,
        ):
            # Prepare timestep tensor for batch
            t_batch = torch.full(
                (x_noisy.shape[0],), t, device=device, dtype=torch.long
            )  # [B]

            # Model prediction: estimate noise at current timestep
            pred_noise = self.model(x_noisy, time=t_batch, z_cond=z_cond, **kwargs)  # [B,C,D]
            
            # Apply scheduler step: remove predicted noise to get x_{t-1}
            x_noisy = self.noise_scheduler.step(pred_noise, t, x_noisy).prev_sample  # [B,C,D]

            # Store intermediate state if requested
            if return_all:
                all_noisy.append(x_noisy)

        # Return final clean sample and optional trajectory
        return x_noisy, all_noisy
