# PVCNN: Point-Voxel CNN for Efficient 3D Deep Learning

Source: [https://github.com/mit-han-lab/pvcnn](https://github.com/mit-han-lab/pvcnn)

See [grasp_ldm/models/modules/ext/pvcnn/benchmark.py](grasp_ldm/models/modules/ext/pvcnn/benchmark.py) for the preliminary comparison between PVCNN and PointNet++.

```
@inproceedings{liu2019pvcnn,
  title={Point-Voxel CNN for Efficient 3D Deep Learning},
  author={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={Advances in Neural Information Processing Systems},
  year={2019}
}
```

## License

This repository is released under the MIT license. See [LICENSE](LICENSE) for additional details.
