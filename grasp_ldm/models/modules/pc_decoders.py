from typing import Sequence

from torch import nn

from .ext.pvcnn.pvcnn_base import PVCNN, PVCNN2
from .ext.pvcnn.utils import (
    create_mlp_components,
    create_pointnet2_fp_modules,
    create_pointnet2_sa_components,
    create_pointnet_components,
)


class PVCNNInvert(nn.Module):
    """
    PVCNN Invert Module for Point Cloud Feature Generation
    
    This module performs the inverse operation of PVCNN encoding, generating
    point cloud features from encoded representations through hierarchical
    point convolution operations.
    
    Pipeline Overview:
    1. Input: Feature tensor [B, C_in, N] where C_in=input_channels, N=num_points
    2. Hierarchical Processing: Apply series of PVConv blocks with different resolutions
    3. Output: Enhanced features [B, C_out, N] and coordinates [B, 3, N]
    """
    
    def __init__(
        self,
        in_channels=3,
        extra_feature_channels=0,
        width_multiplier=1,
        voxel_resolution_multiplier=1,
        scale_channels=0.25,
        scale_voxel_resolution=0.75,
        num_blocks=...,
        extra_block_channels=None,
    ):
        super().__init__()
        assert extra_feature_channels >= 0

        if not isinstance(num_blocks, Sequence):
            raise TypeError("num_blocks must be of type List or Tuple")
        elif len(num_blocks) != 4:
            raise ValueError(
                "PVCNN is configured with 4 PVConv modules. The num_blocks sequence must of length 4."
            )

        # Total input channels including extra features
        self.in_channels = in_channels + extra_feature_channels
        
        # Generate block specifications for hierarchical processing
        self.block_spec = self.get_blocks_spec(
            c_mul=scale_channels,
            r_mul=scale_voxel_resolution,
            num_blocks=num_blocks,
            extra_block_channels=extra_block_channels,
        )

        # Output channels determined by the last block specification
        self.out_channels = self.block_spec[-1][0]

        # Create PointNet-style components for hierarchical feature processing
        layers, channels_point, concat_channels_point = create_pointnet_components(
            blocks=self.block_spec,
            in_channels=self.in_channels,
            with_se=True,
            normalize=False,
            width_multiplier=width_multiplier,
            voxel_resolution_multiplier=voxel_resolution_multiplier,
        )

        # ModuleList containing hierarchical processing layers
        self.point_features = nn.ModuleList(layers)

    def get_blocks_spec(self, c_mul, r_mul, num_blocks, extra_block_channels=None):
        """
        Generate block specifications for PVCNN invert architecture
        
        Args:
            c_mul (float): Channel multiplier for scaling feature dimensions
            r_mul (float): Resolution multiplier for voxel resolution scaling
            num_blocks (tuple): Number of blocks for each hierarchical level
            extra_block_channels (tuple, optional): Additional channels for extra blocks
            
        Returns:
            tuple: Block specifications (channels, num_blocks, voxel_resolution)
        """
        # base_blocks = ((64, 1, 32), (128, 2, 16), (1024, 1, None), (2048, 1, None))
        assert len(num_blocks) == 4 and isinstance(num_blocks, Sequence)

        nb1, nb2, nb3, nb4 = num_blocks

        # Calculate channel dimensions with scaling
        c1 = int(64 * c_mul)
        c2 = int(512 * c_mul)
        c3 = int(256 * c_mul)
        c4 = int(128 * c_mul)  # 512?

        # Calculate voxel resolutions with scaling
        r1 = int(16 * r_mul)
        r2 = int(32 * r_mul)

        # Ensure even channel and resolution values for proper processing
        assert c1 % 2 == 0 and c2 % 2 == 0 and c3 % 2 == 0 and c4 % 2 == 0
        assert r1 % 2 == 0 and r2 % 2 == 0

        if extra_block_channels is None:
            # Standard block configuration
            blocks = ((c1, nb1, r1), (c2, nb2, r2), (c3, nb3, None), (c4, nb4, None))
        else:
            # Extended configuration with extra blocks
            extra_blocks = ((c, 1, None) for c in extra_block_channels)
            blocks = (
                (c1, nb1, None),
                (c2, nb2, None),
                (c3, nb3, r1),
                (c4, nb4, r2),
                *extra_blocks,
            )

        return blocks

    def forward(self, inputs, cond=None):
        """
        Forward pass through PVCNNInvert pipeline
        
        Args:
            inputs (Tensor): Input features [B, C_in, N] where C_in >= 3
            cond (Tensor, optional): Conditioning tensor for conditional processing
            
        Returns:
            tuple: (features [B, C_out, N], coordinates [B, 3, N])
        """
        assert (
            inputs.dim() == 3 and inputs.shape[1] >= 3
        ), f"Invalid inputs: {inputs.shape}"
        
        # Extract coordinates (first 3 channels) and use full input as features
        coords, features = inputs[:, :3, :].contiguous(), inputs

        # Apply hierarchical point feature processing
        for i in range(len(self.point_features)):
            features, coords = self.point_features[i]((features, coords))

        return features, coords


class PVCNN2Invert(nn.Module):
    """
    PVCNN2 Invert Module with Set Abstraction and Feature Propagation
    
    This module implements a more advanced point cloud decoder using PointNet++ style
    Set Abstraction (SA) and Feature Propagation (FP) modules for hierarchical
    feature processing and upsampling.
    
    Pipeline Overview:
    1. Input: Feature tensor [B, C_in, N] where C_in >= 3
    2. Set Abstraction: Hierarchical downsampling with feature extraction
    3. Feature Propagation: Hierarchical upsampling with feature interpolation
    4. Output: Enhanced features [B, C_out, N] and coordinates [B, 3, N]
    
    Architecture:
    - 4 Set Abstraction modules for hierarchical downsampling
    - 4 Feature Propagation modules for hierarchical upsampling
    - Skip connections between SA and FP modules
    """
    
    # Set Abstraction Modules Configuration
    # (PVconv config, SA config)
    # PV Conv config: (out_channels, num_blocks, voxel_resolution),
    # SA config (num_centers, radius, num_neighbors, out_channels))
    # out_channels from SA config are shared MLP channels
    sa_blocks = [
        ((32, 1, 32), (1024, 0.1, 32, (32, 64))),
        ((64, 2, 16), (256, 0.2, 32, (64, 128))),
        ((128, 1, 8), (64, 0.4, 32, (128, 256))),
        (None, (16, 0.8, 32, (256, 256, 512))),
    ]

    # Feature Propagation Modules Configuration
    # (fp config, pvconv config)
    # fp_config: (in_channels, out_channels)
    # PVConv config: (out_channels, num_blocks, voxel_resolution)
    fp_blocks = [
        ((256, 256), (256, 1, 8)),
        ((256, 256), (256, 1, 8)),
        ((256, 128), (128, 2, 16)),
        ((128, 128, 64), (64, 1, 32)),
    ]

    def __init__(
        self,
        in_channels=3,
        extra_feature_channels=0,
        width_multiplier=1,
        voxel_resolution_multiplier=1,
        use_attention=False,
        dropout=0.1,
    ):
        super().__init__()
        # Total input channels including extra features
        self.in_channels = in_channels + extra_feature_channels

        ## Set Abstraction Modules for hierarchical downsampling
        # Create SA modules that progressively downsample points while extracting features
        (
            sa_layers,
            sa_in_channels,
            channels_sa_features,
            _,
        ) = create_pointnet2_sa_components(
            sa_blocks=self.sa_blocks,
            embed_dim=0,
            extra_feature_channels=extra_feature_channels,
            with_se=True,
            voxelization_normalize=True,
            use_attention=use_attention,
            dropout=dropout,
            width_multiplier=width_multiplier,
            voxel_resolution_multiplier=voxel_resolution_multiplier,
        )
        self.sa_layers = nn.ModuleList(sa_layers)

        # Configure input channels for feature propagation
        # Only use extra features in the last fp module
        sa_in_channels[0] = extra_feature_channels

        # Feature Propagation Modules for hierarchical upsampling
        # Create FP modules that progressively upsample features back to original resolution
        fp_layers, channels_fp_features = create_pointnet2_fp_modules(
            fp_blocks=self.fp_blocks,
            in_channels=channels_sa_features,
            sa_in_channels=sa_in_channels,
            with_se=True,
            width_multiplier=width_multiplier,
            voxel_resolution_multiplier=voxel_resolution_multiplier,
        )
        self.fp_layers = nn.ModuleList(fp_layers)

        # Output channels determined by the last PVConv module in FP layers
        self.out_channels = self.fp_layers[-1][-1].out_channels

    def get_blocks_spec(c_mul, r_mul, num_blocks, extra_block_channels=None):
        """
        Static method to get block specifications (delegates to PVCNNInvert)
        
        Returns:
            tuple: Block specifications for PVCNN architecture
        """
        return PVCNNInvert.get_blocks_spec(
            c_mul, r_mul, num_blocks, extra_block_channels
        )

    def forward(self, inputs, cond=None):
        """
        Forward pass through PVCNN2Invert pipeline
        
        Pipeline stages:
        1. Set Abstraction: Progressive downsampling with feature extraction
           - SA1: [B, C, N] -> [B, C1, N1] (N1 < N)
           - SA2: [B, C1, N1] -> [B, C2, N2] (N2 < N1)
           - SA3: [B, C2, N2] -> [B, C3, N3] (N3 < N2)
           - SA4: [B, C3, N3] -> [B, C4, N4] (N4 < N3)
        
        2. Feature Propagation: Progressive upsampling with feature interpolation
           - FP1: [B, C4, N4] + [B, C3, N3] -> [B, C3', N3]
           - FP2: [B, C3', N3] + [B, C2, N2] -> [B, C2', N2]
           - FP3: [B, C2', N2] + [B, C1, N1] -> [B, C1', N1]
           - FP4: [B, C1', N1] + [B, C, N] -> [B, C_out, N]
        
        Args:
            inputs (Tensor): Input features [B, C_in, N] where C_in >= 3
            cond (Tensor, optional): Conditioning tensor for conditional processing
            
        Returns:
            tuple: (features [B, C_out, N], coordinates [B, 3, N])
        """
        assert (
            inputs.dim() == 3 and inputs.shape[1] >= 3
        ), f"Invalid inputs: {inputs.shape}"

        # Extract coordinates (first 3 channels) and use full input as features
        coords, features = inputs[:, :3, :].contiguous(), inputs

        # Storage for intermediate results from Set Abstraction modules
        coords_list, in_features_list = [], []

        # Forward pass through the Set Abstraction Modules
        # Progressive downsampling with feature extraction
        for sa_blocks in self.sa_layers:
            # Store intermediate features and coordinates for skip connections
            in_features_list.append(features)
            coords_list.append(coords)
            # Apply set abstraction: downsample points and extract features
            features, coords = sa_blocks((features, coords))

        # Use extra features (channels 3+) for the first FP module
        in_features_list[0] = inputs[:, 3:, :].contiguous()

        # Forward pass through the Feature Propagation Modules
        # Progressive upsampling with feature interpolation and skip connections
        for fp_idx, fp_blocks in enumerate(self.fp_layers):
            # Apply feature propagation with skip connections
            # Combines current features with stored features from SA modules
            features, coords = fp_blocks(
                (
                    coords_list[-1 - fp_idx],  # Coordinates from corresponding SA level
                    coords,                     # Current coordinates
                    features,                   # Current features
                    in_features_list[-1 - fp_idx],  # Features from corresponding SA level
                )
            )

        return features, coords


class PVCNNDecoder(nn.Module):
    """
    PVCNN-based Point Cloud Decoder
    
    Pipeline Overview:
    1. Input: Encoded features [B, in_features] where B=batch_size, in_features=encoded_feature_dim
    2. Feature Expansion: [B, in_features] -> [B, n_points] expand to point dimension
    3. Dimension Adjustment: [B, n_points] -> [B, 1, n_points] add channel dimension
    4. Channel Expansion: [B, 1, N] -> [B, 16, N] expand channels for PVCNN processing
    5. PVCNN Invert Processing: [B, 16, N] -> [B, C, N] generate point cloud features
    6. Output: [B, C, N] decoded point cloud features where C=output_channels, N=num_points
    """
    
    def __init__(
        self,
        in_features=32,
        in_channels=1,
        n_points=1024,
        extra_feature_channels=0,
        scale_channels=0.25,
        scale_voxel_resolution=0.75,
        num_blocks=(1, 1, 1, 1),
        extra_block_channels=None,
        use_global_attention=True,
    ) -> None:
        """PVCNN decoder

        Args:
            in_features (int, optional): Number of input features. Defaults to 32.
            in_channels (int, optional): Number of input channels. Defaults to 1.
            n_points (int, optional): Number of points in the pointcloud. Defaults to 1024.
            extra_feature_channels (int, optional): Number of extra features to add. Defaults to 0.
            scale_channels (float, optional): Scale factor for the number of channels. Defaults to 0.25.
            scale_voxel_resolution (float, optional): Scale factor for the voxel resolution. Defaults to 0.75.
            num_blocks (tuple, optional): Number of blocks in each scale. Defaults to (1, 1, 1, 1).
            extra_block_channels (tuple, optional): Extra channels (i.e. normals) for each block. Defaults to None.
            use_global_attention (bool, optional): If to use global attention after PVCNN. Defaults to True.
        """
        super().__init__()

        self.in_channels = in_channels
        self.in_features = in_features

        # Number of channels for PVCNN input processing
        self._pvcnn_in_channels = 16

        # Feature dimension expansion layer: [B, in_features] -> [B, n_points]
        # Converts encoded features to point-wise representation
        self.in_layer = nn.Linear(
            in_features=self.in_features,
            out_features=n_points,
        )

        # Channel expansion layer: [B, 1, N] -> [B, 16, N]
        # Expands single channel to multiple channels for PVCNN processing
        self.conv_layer_expand = nn.Sequential(
            # 1D convolution for channel expansion: [B, 1, N] -> [B, 16, N]
            nn.Conv1d(
                in_channels=self.in_channels,
                out_channels=self._pvcnn_in_channels,
                kernel_size=1,
                bias=False,
            ),
            # Batch normalization for stable training
            nn.BatchNorm1d(self._pvcnn_in_channels),
            # ReLU activation for non-linearity
            nn.ReLU(inplace=True),
        )

        # Core PVCNN invert module for point cloud feature generation
        # Processes [B, 16, N] -> [B, C, N] where C is determined by PVCNN configuration
        self.pvcnn_modules = PVCNNInvert(
            in_channels=self._pvcnn_in_channels,
            extra_feature_channels=extra_feature_channels,
            scale_channels=scale_channels,
            scale_voxel_resolution=scale_voxel_resolution,
            num_blocks=num_blocks,
            extra_block_channels=extra_block_channels,
        )

        # Store output channels from PVCNN modules
        self.out_channels = self.pvcnn_modules.out_channels

    def forward(self, out, cond=None):
        """
        Forward pass through PVCNNDecoder pipeline
        
        Dimension transformations:
        1. Feature expansion: [B, in_features] -> [B, n_points]
        2. Dimension adjustment: [B, n_points] -> [B, 1, n_points] (if needed)
        3. Channel expansion: [B, 1, N] -> [B, 16, N]
        4. PVCNN invert processing: [B, 16, N] -> [B, C, N]

        Args:
            out (Tensor): Encoded features [B, in_features] or [B, in_channels, in_features]
            cond (Tensor, optional): Conditioning tensor for conditional decoding

        Returns:
            Tensor: Decoded point cloud features [B, C, N] where C=output_channels, N=num_points
        """
        # Step 1: Feature dimension expansion
        # Transform encoded features to point-wise representation: [B, in_features] -> [B, n_points]
        out = self.in_layer(out)
        
        # Step 2: Ensure proper channel dimension
        # Add channel dimension if needed: [B, n_points] -> [B, 1, n_points]
        out = out.unsqueeze(1) if out.ndim == 2 else out

        # Step 3: Channel expansion for PVCNN processing
        # Expand channels through conv-bn-relu: [B, 1, N] -> [B, 16, N]
        out = self.conv_layer_expand(out)

        # Step 4: PVCNN invert processing
        # Generate point cloud features through hierarchical processing: [B, 16, N] -> [B, C, N]
        feats, coords = self.pvcnn_modules(out, cond)

        return feats


class PVCNN2Decoder(PVCNNDecoder):
    """
    PVCNN2-based Point Cloud Decoder
    
    Extends PVCNNDecoder by replacing the core PVCNN module with PVCNN2Invert,
    which provides more advanced hierarchical processing through Set Abstraction
    and Feature Propagation modules.
    
    Pipeline Overview (inherits from PVCNNDecoder):
    1. Input: Encoded features [B, in_features]
    2. Feature Expansion: [B, in_features] -> [B, n_points]
    3. Dimension Adjustment: [B, n_points] -> [B, 1, n_points]
    4. Channel Expansion: [B, 1, N] -> [B, 16, N]
    5. PVCNN2 Invert Processing: [B, 16, N] -> [B, C, N] (enhanced with SA/FP)
    6. Output: [B, C, N] decoded point cloud features
    
    Key Difference from PVCNNDecoder:
    - Uses PVCNN2Invert instead of PVCNNInvert for more sophisticated feature processing
    - Includes local attention mechanisms in PVConv modules
    - Better handling of hierarchical feature relationships
    """
    
    def __init__(
        self,
        in_features=3,
        out_features=32,
        n_points=1024,
        extra_feature_channels=0,
        scale_channels=0.25,
        scale_voxel_resolution=0.75,
        num_blocks=(1, 1, 1, 1),
        is_conditioned=False,
        cond_dims=None,
        extra_block_channels=None,
        use_global_attention=True,
        use_local_attention=True,
    ) -> None:
        """PVCNN2 Decoder

        Overrides the PVCNN modules in `PVCNNDecoder` to use the PVCNN2Invert model.

        Args:
            in_features (int, optional): Number of input features. Defaults to 3.
            out_features (int, optional): Number of output features. Defaults to 32.
            n_points (int, optional): Number of points in the pointcloud. Defaults to 1024.
            extra_feature_channels (int, optional): Number of extra features to add. Defaults to 0.
            scale_channels (float, optional): Scale factor for the number of channels. Defaults to 0.25.
            scale_voxel_resolution (float, optional): Scale factor for the voxel resolution. Defaults to 0.75.
            num_blocks (tuple, optional): Number of blocks in each scale. Defaults to (1, 1, 1, 1).
            is_conditioned (bool, optional): If the model is conditioned on extra data. Defaults to False.
            cond_dims (int, optional): Conditioning dimensions. Defaults to None.
            extra_block_channels (tuple, optional): Extra channels (i.e. normals) for each block. Defaults to None.
            use_global_attention (bool, optional): If to use global attention after PVCNN. Defaults to True.
            use_local_attention (bool, optional): If to use local attention in PVConv modules. Defaults to True.
        """
        # Initialize parent class with all standard decoder components
        super().__init__(
            in_features=in_features,
            out_features=out_features,
            n_points=n_points,
            extra_feature_channels=extra_feature_channels,
            scale_channels=scale_channels,
            scale_voxel_resolution=scale_voxel_resolution,
            num_blocks=num_blocks,
            is_conditioned=is_conditioned,
            cond_dims=cond_dims,
            extra_block_channels=extra_block_channels,
            use_global_attention=use_global_attention,
        )

        # Override core PVCNN module with advanced PVCNN2Invert
        # This replaces the standard PVCNNInvert with a more sophisticated version
        # that includes Set Abstraction and Feature Propagation modules
        self.pvcnn_modules = PVCNN2Invert(
            extra_feature_channels=extra_feature_channels,
            scale_channels=scale_channels,
            scale_voxel_resolution=scale_voxel_resolution,
            num_blocks=num_blocks,
            is_conditioned=is_conditioned,
            cond_dims=cond_dims,
            extra_block_channels=extra_block_channels,
            use_attention=use_local_attention,
        )
