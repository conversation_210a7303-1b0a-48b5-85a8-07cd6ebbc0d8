"""
Trainer module initialization and enumeration for GraspLDM.

This module provides a centralized way to manage different types of trainers
used in the GraspLDM project, including classification, VAE, and diffusion model trainers.
"""

import enum

from pytorch_lightning.loggers import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>sor<PERSON>oard<PERSON>og<PERSON>, <PERSON>db<PERSON>ogger

# Dictionary mapping logger names to their corresponding PyTorch Lightning logger classes
# Supports multiple logging backends for experiment tracking and monitoring
LOGGERS = {
    "WandbLogger": WandbLogger,          # Weights & Biases logger for cloud-based experiment tracking
    "TensorBoardLogger": TensorBoardLogger,  # TensorBoard logger for local visualization
    "CSVLogger": CSVLogger,              # CSV logger for simple file-based logging
}


class E_Trainers(enum.Enum):
    """
    Enumeration class for different trainer types in the GraspLDM framework.
    
    This enum provides a type-safe way to specify and retrieve different trainer
    implementations for various model architectures used in grasp learning.
    
    Supported trainer types:
    - CLASSIFIER: For grasp classification tasks
    - VAE: For Variational Autoencoder-based grasp generation
    - DDM: For Denoising Diffusion Model-based grasp generation
    """
    
    CLASSIFIER = "classifier"  # Trainer for grasp classification models
    VAE = "vae"               # Trainer for Variational Autoencoder models
    DDM = "ddm"               # Trainer for Denoising Diffusion Models

    def __repr__(self):
        """
        Return a string representation of the enum value.
        
        Returns:
            str: Formatted string showing the class name and enum member name
        """
        return f"{self.__class__.__name__}.{self.name}"

    def _get_trainer(model_type: str):
        """
        Internal static method to retrieve trainer class based on model type.
        
        This method dynamically imports and returns the appropriate trainer class
        to avoid circular imports and reduce memory usage.
        
        Args:
            model_type (str): The model type identifier (should be an E_Trainers enum value)
            
        Returns:
            class: The corresponding trainer class
            
        Raises:
            NotImplementedError: If the model type is not supported
        """
        if model_type == E_Trainers.CLASSIFIER:
            # Import grasp classification trainer for binary/multi-class grasp prediction
            from grasp_ldm.trainers.grasp_classification_trainer import (
                GraspClassificationTrainer,
            )
            return GraspClassificationTrainer
            
        elif model_type == E_Trainers.VAE:
            # Import VAE trainer for latent space-based grasp generation
            from grasp_ldm.trainers.grasp_generation_trainer import GraspVAETrainer
            return GraspVAETrainer
            
        elif model_type == E_Trainers.DDM:
            # Import diffusion model trainer for iterative denoising-based grasp generation
            from grasp_ldm.trainers.grasp_generation_trainer import GraspLDMTrainer
            return GraspLDMTrainer
            
        else:
            raise NotImplementedError(f"Model type {model_type} not implemented")

    def get_trainer(self):
        """
        Instance method to get the trainer class for this enum value.
        
        Returns:
            class: The trainer class corresponding to this enum instance
        """
        return E_Trainers._get_trainer(self)

    def from_string(model_type: str):
        """
        Static method to convert string representation to enum value.
        
        Args:
            model_type (str): String identifier for the model type
            
        Returns:
            E_Trainers: The corresponding enum value
            
        Raises:
            NotImplementedError: If the model type string is not recognized
        """
        if model_type == "classifier":
            return E_Trainers.CLASSIFIER
        elif model_type == "vae":
            return E_Trainers.VAE
        elif model_type == "ddm":
            return E_Trainers.DDM
        else:
            raise NotImplementedError(f"Model type {model_type} not implemented")

    def get(model_type: str):
        """
        Static method to directly get trainer class from string identifier.
        
        This is a convenience method that combines from_string() and _get_trainer()
        to provide a one-step conversion from string to trainer class.
        
        Args:
            model_type (str): String identifier for the model type
            
        Returns:
            class: The trainer class corresponding to the model type
            
        Raises:
            NotImplementedError: If the model type is not supported
        """
        enum_type = E_Trainers.from_string(model_type)
        return E_Trainers._get_trainer(enum_type)
